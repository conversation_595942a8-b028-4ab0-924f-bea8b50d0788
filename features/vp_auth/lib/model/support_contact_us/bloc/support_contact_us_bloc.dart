import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:vp_common/vp_common.dart';

part 'support_contact_us_event.dart';

part 'support_contact_us_state.dart';

class SupportContactUsBloc
    extends Bloc<SupportContactUsEvent, SupportContactUsState> {
  SupportContactUsBloc()
      : super(
          SupportContactUsInfo(
              phone: '',
              email: '',
              address: '',
              zalo: '',
              messenger: '',
              website: '',
              linkedIn: ''),
        ) {
    on<OnLoadSupportContactInfo>(_loadSupportContactInfo);
  }

  void _loadSupportContactInfo(OnLoadSupportContactInfo event,
      Emitter<SupportContactUsState> emit) async {
    const phone = '1900 636679';
    const email = '<EMAIL>';
    const address =
        'Tầng 25, Tòa nhà Văn phòng Thương m<PERSON>, số 89 Láng <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>hố <PERSON>';

    emit(
      SupportContactUsInfo(
        phone: phone,
        email: email,
        address: address,
        zalo: AppConstants.zalo,
        messenger: AppConstants.messenger,
        website: AppConstants.website,
        linkedIn: AppConstants.linkedIn,
      ),
    );
  }
}
