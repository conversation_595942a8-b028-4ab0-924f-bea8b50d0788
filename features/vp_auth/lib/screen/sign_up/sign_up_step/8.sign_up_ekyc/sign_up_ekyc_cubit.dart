import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:app/common/error/show_error.dart';
import 'package:app/common/utils/app_device_id.dart';
import 'package:app/common/utils/app_log_utils.dart';
import 'package:app/common/utils/app_routes_utils/app_routes_utils.dart';
import 'package:app/common/utils/app_snackbar_utils.dart';
import 'package:app/common/widgets/vpbank_loading.dart';
import 'package:app/global/build_config/stock_build_config.dart';
import 'package:app/global/navigator/app_router.dart';
import 'package:app/global/session/app_session.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/data/model/model_ekyc_fis/live_ness_model.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/models/onboarding_users_ekyc_action_obj.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/onboarding_path_api.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/onboarding_repository.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/cubit/sign_up_step.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_tracking.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_utils.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/4.1sign_up_nfc/widget/popup_warning.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/i_sign_up_step.dart';
import 'package:app/packages/shared/modules/partner_connection/common/lang/partner_connection_key_lang.dart';
import 'package:app/packages/shared/modules/partner_connection/common/lang/partner_connection_localized_value.dart';
import 'package:app/packages/shared/modules/settings/assets/settings_key_assets.dart';
import 'package:collection/collection.dart';
import 'package:core/di/injector.dart';
import 'package:ekyc_plugin_flutter/ekyc_plugin_flutter.dart';
import 'package:equatable/equatable.dart';
import 'package:camera/camera.dart';

import 'package:app/packages/bond/data/models/base_response_message.dart';
import 'package:app/packages/shared/modules/partner_connection/presentation/feature/open_account/vpbank/app_camera/app_camera_page.dart';
import 'package:app/packages/shared/modules/partner_connection/presentation/feature/open_account/widget/dialog_error.dart';
import 'package:flutter/material.dart';

part 'sign_up_ekyc_state.dart';

class SignUpEkycCubit extends ISignUpController<SignUpEkycState> {
  SignUpEkycCubit() : super(const SignUpEkycState());

  @override
  String? get title => getAccountLang(AccountKeyLang.eKycTitle);

  @override
  int? get progress => SignUpStep.ekyc.process;

  @override
  bool isTypeScreen(SignUpTypeScreen value) {
    return value == SignUpTypeScreen.ekyc;
  }

  @override
  void onNext(SignUpTypeScreen type) {
    checkEmitEkycLiveness();
  }

  bool isCanEkyc() {
    return state.isValid ?? false;
  }

  void checkEmitEkyc(dynamic value) {
    String msgEkyc = '';
    final _isValid = (value is String) ? value.isEmpty : null;
    if (_isValid == false) {
      msgEkyc = getAccountLang(AccountKeyLang.verifyFaceFail);
    }
    setEnable(_isValid ?? false);
    (_isValid ?? false)
        ? SignUpTracking().signupFaceRegSuccess()
        : SignUpTracking().signupFaceRegFail();

    emit(state.copyWith(isValid: _isValid, message: msgEkyc));
  }

  void checkEmitEkycLiveness() {
    SignUpData().attachDataObj.setEnableFaceLiveness(true);
    setEnable(true);
    emit(state.copyWith(isValid: true, message: ''));
    SignUpRouter().onPush(SignUpTypeScreen.signature);
  }

  void updateEnableButtonLiveness() {
    emit(state.copyWith(
        isValid: SignUpData().attachDataObj.getEnableFaceLiveness()));
  }

  Future<OnboardingUsersAccEkycActionResponseObj?>
      callAPIGetListAction() async {
    OnboardingUsersAccEkycActionResponseObj? obj;
    try {
      showDialogLoading();
      final value =
          await sl.get<OnboardingRepository>().onboardingUsersEkycAction();

      if (value != null) {
        if (value.isSuccess()) {
          obj = value.data;
        } else {
          showMessage(value.message);
        }
      }
    } catch (e) {
      showError(e);
    } finally {
      hideDialogLoading();
    }
    return obj;
  }

  OnboardingRepository get repository => sl.get<OnboardingRepository>();

  Future<void> gotoFaceLiveness(BuildContext context) async {
    final obj = SignUpData().attachDataObj;
    dlog('Sangdv face ${obj.getBase64FrontIdCard()}');
    Map ressss = await SampleCallNativeFlutter.showLiveness(
      '${Session().onboardingToken}',
      'Transcode',
      sl.get<StockBuildConfigs>().iamUrl() +
          OnboardingPathAPI.onboardingLivenessV2,
      'vi',
      obj.getBase64FrontIdCard(),
      headers: {
        "x-device": await AppUtils.getFpjsDeviceId(),
        "x-devicetype": Platform.isAndroid ? "Android" : "IOS",
        "x-lang": "vi",
        "x-via": "Y",
      },
    ) as Map;

    ResponseLivenessResponse response =
        ResponseLivenessResponse.fromJson(ressss);
    if (response.error?.type == 'UNKNOWN_ERROR') {
      popupWarning(navigation.navigatorKey.currentContext!,
          onBack: () => SignUpRouter().onBackUtil(SignUpTypeScreen.phone));

      return;
    }
    ResponseLiveness liveNessModel =
        ResponseLiveness.fromJson(json.decode(ressss['response']));
    if (liveNessModel.code == 'IABNOT000') {
      await Future.delayed(const Duration(milliseconds: 1000));
      checkEmitEkycLiveness();
    } else {
      showSnackBar(context, liveNessModel.message ?? '');
    }
  }

  Future<void> gotoCameraSignature(BuildContext context) async {
    final isSuccess = await SignUpPermissionUtils.checkPermissionCamera();
    if (isSuccess) {
      final cameras = await availableCameras();
      final camera = cameras.firstWhereOrNull(
          (element) => element.lensDirection == CameraLensDirection.front);

      if (cameras.isNotEmpty && camera != null) {
        RouterUtils.push(
            navigation.navigatorKey.currentContext!,
            AppCameraPage(
              camera: camera,
              title: 'Chụp ảnh chân dung',
              nameFileSave: 'IDCARD_SIGNATURE',
              onTap: (pathSave) async {
                if (pathSave != null && pathSave is String) {
                  try {
                    showDialogLoading();
                    final obj = await sl
                        .get<OnboardingRepository>()
                        .updateAvatar(pathFile: pathSave) as BEBaseResponse;
                    hideDialogLoading();
                    if (obj.isSuccess()) {
                      navigation.goBack();
                      navigation.goBack();
                      showDialogLoading();
                      await Future.delayed(const Duration(seconds: 2));
                      hideDialogLoading();
                      gotoFaceLiveness(context);
                    } else {
                      final message = await getErrorMessage(obj);
                      showDialogError(
                          img: SettingsKeyAssets.icError,
                          navigation.navigatorKey.currentContext!,
                          getPartnerConnectionLang(
                              PartnerConnectionLangKey.notification),
                          message ?? '', pressRight: () {
                        navigation.goBack();
                      });
                    }
                  } catch (e) {
                    hideDialogLoading();
                    final message = await getErrorMessage(e);
                    showDialogError(
                        img: SettingsKeyAssets.icError,
                        navigation.navigatorKey.currentContext!,
                        getPartnerConnectionLang(
                            PartnerConnectionLangKey.notification),
                        message ?? '', pressRight: () {
                      navigation.goBack();
                    });
                  }
                }
              },
            ),
            dataBack: (pathSave) {});
      }
    }
  }
}
