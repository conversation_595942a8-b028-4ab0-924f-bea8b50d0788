import 'package:app/common/constants/app_constants.dart';
import 'package:app/common/constants/key_shared.dart';
import 'package:app/packages/shared/modules/account/common/assets/account_key_assets.dart';
import 'package:app/packages/shared/modules/tutorial/presentation/utils.dart';
import 'package:core/core.dart';
import 'package:core_ui/core_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';

class SignUpDatePicker extends StatefulWidget {
  const SignUpDatePicker(
      {Key? key,
      required this.strDate,
      required this.pickerDate,
      this.enable = true})
      : super(key: key);

  final String strDate;
  final Function(String) pickerDate;
  final bool? enable;

  @override
  State<SignUpDatePicker> createState() => _AccountDatePickerState();
}

class _AccountDatePickerState extends State<SignUpDatePicker> {
  late DateTime date;
  String contentDate = '';

  final forMatDate = 'dd/MM/yyyy';

  @override
  void initState() {
    super.initState();
    try {
      date = DateFormat(forMatDate).parse(widget.strDate);
    } catch (e) {
      date = DateTime.now();
    }
    contentDate = getText();
  }

  @override
  Widget build(BuildContext context) {
    bool enable = widget.enable ?? true;
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: Stack(
        children: [
          Container(
              height: 52,
              decoration: BoxDecoration(
                  color: enable ? ColorUtils.bgMain : ColorUtils.highlightBg,
                  border: Border.all(
                      width: 1,
                      color:
                          enable ? ColorUtils.gray700 : ColorUtils.transparent),
                  borderRadius: BorderRadius.circular(SizeUtils.kRadius8)),
              child: Row(
                children: [
                  const SizedBox(width: 16),
                  Expanded(
                      child: Text(contentDate,
                          style: vpTextStyle.body14?.copyWith(
                              color: enable
                                  ? ColorUtils.black
                                  : ColorUtils.gray500))),
                  SvgPicture.asset(AccountKeyAssets.icDatePicker,
                      color: enable ? ColorUtils.gray900 : ColorUtils.gray300),
                  const SizedBox(
                    width: 16,
                  )
                ],
              )),
          SizedBox(
            height: 52,
            child: InkWell(
              onTap: (() {
                // pickDate(context);
              }),
            ),
          )
        ],
      ),
    );
  }

  Future pickDate(BuildContext context) async {
    bool isLangVi = Utils.isVi;
    final newDate = await showDatePicker(
        locale: Locale(isLangVi ? AppConstants.vi : AppConstants.en),
        context: context,
        initialDate: date,
        firstDate: DateTime(DateTime.now().year - 100),
        lastDate: DateTime.now(),
        builder: (context, child) {
          return Theme(
            data: ThemeData.light().copyWith(
              colorScheme: ColorScheme.light(
                primary: ColorUtils.primary,
              ),
            ),
            child: child!,
          );
        });

    if (newDate == null) return;
    setState(() {
      date = newDate;
      contentDate = getText();
    });
    widget.pickerDate(contentDate);
  }

  String getText() {
    return DateFormat(forMatDate).format(date);
  }
}
