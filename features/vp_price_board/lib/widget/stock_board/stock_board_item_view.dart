import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_price_board/widget/stock_board/helper/stock_board_ui_helper.dart';
import 'package:vp_price_board/widget/stock_board/helper/stock_board_item_helper.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

part 'symbol_view.dart';

part 'close_price_view.dart';

part 'price_change_view.dart';

part 'percent_change_view.dart';

part 'vol_view.dart';

part 'diff_view.dart';

class StockBoardItemView extends StatelessWidget {
  const StockBoardItemView({
    required this.item,
    required this.columns,
    this.marketInfo,
    this.onEventTap,
    this.onTap,
    super.key,
  });

  final StockInfoModel item;

  /// only use for derivative tab
  final MarketInfoModel? marketInfo;

  final List<SortColumn> columns;

  final Function(StockInfoModel stock)? onTap;

  final Function(StockInfoModel stock)? onEventTap;

  int getFlexBy(StockInfoFieldType type) {
    return columns.firstWhere((e) => e.sortType == type).flex;
  }

  Widget mapItemView(SortColumn column) {
    switch (column.sortType) {
      case StockInfoFieldType.symbol:
        return Expanded(
          flex: getFlexBy(StockInfoFieldType.symbol),
          child: _SymbolView(item: item, onEventTap: onEventTap),
        );
      case StockInfoFieldType.closePrice:
        return Expanded(
          flex: getFlexBy(StockInfoFieldType.closePrice),
          child: _ClosePriceView(item: item),
        );

      case StockInfoFieldType.change:
        return Expanded(
          flex: getFlexBy(StockInfoFieldType.change),
          child: _PriceChangeView(item: item),
        );
      case StockInfoFieldType.percentChange:
        return Expanded(
          flex: getFlexBy(StockInfoFieldType.percentChange),
          child: _PercentChangeView(item: item),
        );
      case StockInfoFieldType.vol:
        return Expanded(
          flex: getFlexBy(StockInfoFieldType.vol),
          child: _VolView(item: item),
        );
      case StockInfoFieldType.diff:
        return Expanded(
          flex: getFlexBy(StockInfoFieldType.diff),
          child: _DiffView(item: item, marketIndex: marketInfo?.marketIndex),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => onTap?.call(item),
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          border: Border(bottom: BorderSide(color: vpColor.strokeNormal)),
        ),
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
        child: Row(children: columns.map((e) => mapItemView(e)).toList()),
      ),
    );
  }
}
