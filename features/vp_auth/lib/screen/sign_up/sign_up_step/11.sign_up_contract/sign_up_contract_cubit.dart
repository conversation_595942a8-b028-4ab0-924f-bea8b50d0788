import 'dart:async';

import 'package:app/common/assets/common_key_assets.dart';
import 'package:app/common/error/show_error.dart';
import 'package:app/common/utils/app_snackbar_utils.dart';
import 'package:app/common/utils/download_file_manager/base_download_file_manager.dart';
import 'package:app/common/utils/download_file_manager/download_file_manager.dart';
import 'package:app/common/widgets/vpbank_loading.dart';
import 'package:app/global/analytics/app_tracking.dart';
import 'package:app/global/analytics/app_tracking_event.dart';
import 'package:app/global/analytics/utils/appsflyer_utils.dart';
import 'package:app/global/navigator/app_router.dart';
import 'package:app/packages/centralize_OTP/helper/utils.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/models/onboarding_users_generate_contract_obj.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/onboarding_repository.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/cubit/sign_up_step.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_tracking.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/i_sign_up_step.dart';
import 'package:app/packages/shared/modules/account/presentation/router/account_router.dart';
import 'package:core/di/injector.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:tuple/tuple.dart';

part 'sign_up_contract_state.dart';

class SignUpContractCubit extends ISignUpController<SignUpContractState> {
  SignUpContractCubit() : super(const SignUpContractState());

  @override
  String? get title => getAccountLang(AccountKeyLang.contractAndTerm);

  @override
  int? get progress => SignUpStep.contract.process;

  @override
  String? get buttonNextContent => getAccountLang(AccountKeyLang.confirm);

  @override
  bool isTypeScreen(SignUpTypeScreen value) {
    return value == SignUpTypeScreen.contract;
  }

  @override
  void onNext(SignUpTypeScreen type) async {
    showLoading();

    final tuple3 = await genOnboardingOTP();

    hideLoading();

    final isSuccess = tuple3.item1;

    final errorMessage = tuple3.item2;

    if (isSuccess) {
      CentralizeUtils().verifySmsOTP(
        onSubmitPinCode: (otp, __, ___, ____) {
          return callApiOpenAccount(otp!);
        },
        onResendSmsOTP: (__, ___) {
          return genOnboardingOTP();
        },
        onSuccess: (responseApi) {
          navigation.replaceTo(
            AccountRouter.registerSuccess,
            arguments: SignUpData(),
          );
        },
        onFail: (e) => showError(e),
        appbarSubTitle: getAccountLang(AccountKeyLang.register),
      );
    } else {
      showErrorMessage(errorMessage);
    }
  }

  Future<Tuple3<bool, String?, Response?>> genOnboardingOTP() async {
    try {
      await repository.genOnboardingOTP();

      return const Tuple3(true, null, null);
    } catch (e) {
      final errorMessage = await getErrorMessage(e);

      return Tuple3(false, errorMessage, null);
    }
  }

  OnboardingRepository get repository => sl.get<OnboardingRepository>();

  /// List hop dong
  List<OnboardingUsersGenerateContractObj> listContract = [];

  // Call Api AddRegister -  Open Account
  Future<Tuple3<bool, String?, Response?>> callApiOpenAccount(
      String otp) async {
    try {
      final response = await repository.onboardingUsersNew(otp);

      SignUpTracking().signupContractSuccess();

      tracking(response.data);

      return Tuple3(true, null, response);
    } catch (e) {
      final errorMessage = await getErrorMessage(e);

      return Tuple3(false, errorMessage, null);
    }
  }

  Future tracking(dynamic data) async {
    try {
      final userId = getUserId(data);

      if (userId.isNotEmpty) Appsflyer().setUserId(userId);

      await Future.delayed(const Duration(milliseconds: 500));

      AppTracking.instance.logEvent(
        name: AppTrackingEvent.registerSuccess,
        type: TrackingType.appsflyer,
      );
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  String getUserId(dynamic data) {
    String result = '';
    if (data is Map) {
      final userId = data['user_id'];
      result = userId == null ? '' : '$userId';
    }
    return result;
  }

  // Reload list hop dong
  void reloadListContract(int index) {
    if (listContract[index].isSelected) {
      listContract[index].isSelected = false;
      return;
    }
    for (var i = 0; i < listContract.length; i++) {
      listContract[i].isSelected = false;
    }
    if (listContract.isNotEmpty) {
      listContract[index].isSelected = true;
    }
  }

  // Dowload hop dong from Assets
  void downloadContractFromAssets(int index) async {
    final contract = listContract[index];
    try {
      showDialogLoading();

      final downloadResult = await DownloadFileManager.instance.downloadFile(
        url: contract.url ?? '',
        fileName: contract.templateCode ?? 'VPBanks',
        extension: FileExtension.pdf,
      );

      showSnackBar(
          navigation.navigatorKey.currentContext!,
          downloadResult != null
              ? getAccountLang(AccountKeyLang.contractSucess)
              : getAccountLang(AccountKeyLang.contractFailed),
          asset: downloadResult != null
              ? CommonKeyAssets.icSuccess
              : CommonKeyAssets.icFail);
    } catch (e) {
      showError(e);
    } finally {
      hideDialogLoading();
    }
  }

  final controllerShowFile = StreamController<bool>();

  void sinkShowFile(bool value) {
    controllerShowFile.sink.add(value);
  }

  /// API khoi tao hop dong
  void apiGenerationContract() async {
    try {
      emit(state.copyWith(isLoading: true));
      final response =
          await sl.get<OnboardingRepository>().onboardingContractGeneration();
      if (response != null && response.data != null) {
        if (response.isSuccess()) {
          listContract = response.data ?? [];
          for (int i = 0; i <= listContract.length - 1; i++) {
            final requestId = listContract[i].requestId ?? '';
            if (requestId.isNotEmpty) {
              await callAPIGetFile(requestId, (url) {
                listContract[i].url = url;
              });
            }
          }
          sinkShowFile(true);
          setEnable(true);
          emit(state.copyWith(isLoading: false));
        } else {
          showMessage(response.message);
        }
      }
    } catch (e) {
      emit(state.copyWith(isLoading: false));
      showError(e);
    }
  }

  Future callAPIGetFile(String requestId, Function(String) callBackUrl) async {
    String url = '';
    try {
      final response =
          await sl.get<OnboardingRepository>().onboardingFiles(requestId);
      if (response != null) {
        if (response.isSuccess()) {
          url = response.data?.url ?? '';
        } else {
          showMessage(response.message);
        }
      }
    } catch (e) {
      showError(e);
    } finally {
      callBackUrl(url);
    }
  }

  @override
  Future<void> close() {
    controllerShowFile.close();
    return super.close();
  }
}
