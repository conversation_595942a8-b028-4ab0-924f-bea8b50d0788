import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart' hide Throttle;
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/cubit/order_edit/order_edit_cubit.dart';
import 'package:vp_trading/cubit/place_order/available_trade/available_trade_cubit.dart';
import 'package:vp_trading/cubit/place_order/stock_info/stock_info_cubit.dart';
import 'package:vp_trading/model/enum/order_lot_type.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';
import 'package:vp_trading/screen/order_container/normal_order/widget/edit_command_bottom_sheet.dart';

/// Wrapper class that provides necessary BLoCs for EditCommandBottomSheet
class EditCommandBottomSheetWrapper extends StatefulWidget {
  final Function() onEditSuccess;
  final Function(String messageEror) onEditFailure;
  final OrderBookModel model;

  const EditCommandBottomSheetWrapper({
    super.key,
    required this.onEditSuccess,
    required this.onEditFailure,
    required this.model,
  });

  @override
  State<EditCommandBottomSheetWrapper> createState() =>
      _EditCommandBottomSheetWrapperState();
}

class _EditCommandBottomSheetWrapperState
    extends State<EditCommandBottomSheetWrapper> {
  late Throttle _availableTradeThrottle;

  @override
  void initState() {
    super.initState();
    _availableTradeThrottle = Throttle(const Duration(milliseconds: 500));
  }

  @override
  void dispose() {
    _availableTradeThrottle.dispose();
    super.dispose();
  }

  OrderType _getOrderTypeFromPriceType(String? priceType) {
    switch (priceType?.toLowerCase()) {
      case 'lo':
      case 'limit':
        return OrderType.lo;
      case 'buyin':
      case 'buy_in':
        return OrderType.buyIn;
      case 'stoploss':
      case 'stop_loss':
        return OrderType.stopLoss;
      case 'takeprofit':
      case 'take_profit':
        return OrderType.takeProfit;
      case 'gtc':
        return OrderType.gtc;
      default:
        return OrderType.lo; // Default to limit order
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => OrderEditCubit()),
        BlocProvider(
          create:
              (context) =>
                  AvailableTradeCubit()..getAvailableTrade(
                    accountId: widget.model.accountId ?? '',
                    symbol: widget.model.symbol ?? '',
                    quotePrice: widget.model.price,
                  ),
        ),
        BlocProvider(
          create:
              (context) => StockInfoCubit(
                orderLotType:
                    (widget.model.qty ?? 0).isOddLot
                        ? OrderLotType.oddLot
                        : OrderLotType.roundLot,
              )..loadData(widget.model.symbol ?? ''),
        ),
      ],
      child: BlocListener<OrderEditCubit, OrderEditState>(
        listener: (context, state) {
          if (state.status == OrderEditStatus.loading) {}
          if (state.status == OrderEditStatus.success) {
            widget.onEditSuccess.call();
          } else if (state.status == OrderEditStatus.failure) {
            widget.onEditFailure.call(state.errorMessage ?? '');
          }
        },
        child: BlocBuilder<StockInfoCubit, StockInfoState>(
          builder: (contextCubit, stockState) {
            return Stack(
              children: [
                VPLoadingBuilder(
                  showLoading: stockState.status.isLoading,
                  builder: (context, child) {
                    if (stockState.stockInfo == null) {
                      return SizedBox(
                        width: MediaQuery.of(context).size.width,
                        height: MediaQuery.of(context).size.height * 0.5,
                        child: const Center(child: VPBankLoading()),
                      );
                    }
                    return EditCommandBottomSheet(
                      stockCode: widget.model.symbol ?? '',
                      currentPrice:
                          double.tryParse(widget.model.price ?? '0') ?? 0,
                      currentVolume:
                          widget.model.qty ?? 0, // khối lượng lệnh đặt
                      executedVolume:
                          widget.model.execQty ?? 0, // Khối lượng đã khớp
                      orderAction:
                          widget.model.orderTypeEnum == OrderTypeEnum.buy
                              ? OrderAction.buy
                              : OrderAction.sell,
                      orderType: _getOrderTypeFromPriceType(
                        widget.model.priceType,
                      ),
                      onConfirm: (newPrice, newVolume) {
                        if (context.read<OrderEditCubit>().state.status ==
                            OrderEditStatus.loading) {
                          return;
                        }
                        context.read<OrderEditCubit>().modifyOrder(
                          orderId: widget.model.orderId ?? '',
                          accountId: widget.model.accountId ?? '',
                          symbol: widget.model.symbol ?? '',
                          newPrice: newPrice,
                          newVolume: newVolume,
                          market: 'equity',
                        );
                      },
                      stockInfo: stockState.stockInfo!,
                      reCallAvailableTrade: (newPrice) async {
                        _availableTradeThrottle(() {
                          contextCubit
                              .read<AvailableTradeCubit>()
                              .getAvailableTrade(
                                accountId: widget.model.accountId ?? '',
                                symbol: widget.model.symbol ?? '',
                                quotePrice: newPrice,
                              );
                        });
                      },
                    );
                  },
                ),
                if (contextCubit.watch<OrderEditCubit>().state.status ==
                    OrderEditStatus.loading) ...[
                  const Center(child: VPBankLoading()),
                ],
              ],
            );
          },
        ),
      ),
    );
  }
}
