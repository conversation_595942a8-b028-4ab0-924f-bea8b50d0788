import 'package:app/packages/shared/modules/account/common/assets/account_key_assets.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/9.sign_up_bank/components/sign_up_search_view.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/9.sign_up_bank/components/sign_up_select_bank/sign_up_select_bank_bloc.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/9.sign_up_bank/components/sign_up_select_bank/sign_up_select_bank_item.dart';
import 'package:core_ui/core_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class SignUpSelectBank<T> extends StatefulWidget {
  const SignUpSelectBank({Key? key, required this.list}) : super(key: key);

  final List<T> list;

  @override
  State<SignUpSelectBank> createState() => _SignUpSelectBankState();
}

class _SignUpSelectBankState extends State<SignUpSelectBank> {
  final bloc = SignUpSelectBankBloc();

  final textController = TextEditingController();

  @override
  void initState() {
    super.initState();
    bloc.list = widget.list;
  }

  @override
  void dispose() {
    bloc.dispose();
    textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    const radius = Radius.circular(24);
    return Container(
      height: MediaQuery.of(context).size.height * 0.5,
      padding: const EdgeInsets.only(top: 24, left: 24, right: 24),
      decoration: BoxDecoration(
          color: ColorUtils.bgPopup,
          borderRadius:
              const BorderRadius.only(topLeft: radius, topRight: radius)),
      child: Column(
        children: [
          SvgPicture.asset(AccountKeyAssets.handle),
          const SizedBox(height: SizeUtils.kRadius16),
          SignUpSearchView(
            textEditingController: textController,
            content: getAccountLang(bloc.isFindBranchBank()
                ? AccountKeyLang.findBranchBank
                : AccountKeyLang.searchBank),
            onChanged: (value) => bloc.searchBank(value),
          ),
          const SizedBox(height: SizeUtils.kRadius8),
          buildListViewBank()
        ],
      ),
    );
  }

  StreamBuilder buildListViewBank() {
    return StreamBuilder(
        stream: bloc.streamSelectBank,
        builder: (context, snapshot) {
          final hasData = snapshot.data ?? false;
          return Expanded(
              child: ((bloc.listSearch.isEmpty && hasData) || bloc.list.isEmpty)
                  ? Padding(
                      padding: const EdgeInsets.all(24),
                      child: Text(getAccountLang(AccountKeyLang.noResultSearch),
                          style: TextStyleUtils.text14Weight500
                              .copyWith(color: ColorUtils.gray500)),
                    )
                  : ListView.builder(
                      itemCount:
                          hasData ? bloc.listSearch.length : bloc.list.length,
                      itemBuilder: (context, index) {
                        return SignUpSelectBankItem(
                          item: hasData
                              ? bloc.listSearch[index]
                              : bloc.list[index],
                          callback: (bankSelectObj) {
                            Navigator.pop(context, bankSelectObj);
                          },
                        );
                      }));
        });
  }
}
