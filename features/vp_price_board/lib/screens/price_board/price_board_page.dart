import 'package:flutter/material.dart';
import 'package:vp_common/extensions/list_extensions.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/gen/assets.gen.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_price_board/model/enum/priceboard_tab.dart';
import 'package:vp_price_board/vp_price_board_navigator.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/vp_stock_common_navigator.dart';

import 'tabs/price_board_tab_view.dart';

class PriceBoardPage extends StatefulWidget {
  const PriceBoardPage({super.key});

  @override
  State<PriceBoardPage> createState() => _PriceBoardPageState();
}

class _PriceBoardPageState extends State<PriceBoardPage>
    with AppLifecycleMixin<PriceBoardPage>, SingleTickerProviderStateMixin {
  int key = 0;

  PriceBoardTabViewEnum? tab;

  late TabController tabController = TabController(
    length: PriceBoardTabViewEnum.values.length,
    vsync: this,
  );

  @override
  void initState() {
    super.initState();

    tabController.addListener(tabListener);
  }

  void tabListener() {
    final index = tabController.index;

    tab = PriceBoardTabViewEnum.values.getElementAt(index);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, state) {
        return VPScaffold(
          key: ValueKey(state),
          backgroundColor: vpColor.backgroundElevationMinus1,
          appBar: VPAppBar.layer(
            title: 'Bảng giá',
            leading: BlocBuilder<AuthCubit, AuthState>(
              builder: (context, state) {
                return state.status != AuthStatus.logined
                    ? IconButton(
                      onPressed: () async {
                        context.push('/signIn');
                      },
                      icon: Icon(Icons.person, color: themeData.gray500),
                    )
                    : IconButton(
                      onPressed: () {
                        context.safePop();
                      },
                      icon: Assets.icons.appbar.icBack.svg(
                        colorFilter: ColorFilter.mode(
                          vpColor.iconPrimary,
                          BlendMode.srcIn,
                        ),
                      ),
                    );
              },
            ),
            actions: [
              IconButton(
                onPressed: () => openSearchPage(context),
                icon: Assets.icons.icSearch.svg(
                  colorFilter: ColorFilter.mode(
                    vpColor.iconPrimary,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ],
          ),
          body: Column(
            key: ValueKey(key),
            children: [
              const SizedBox(height: 12),

              const MarketIndexList(),

              const SizedBox(height: 12),

              Expanded(child: PriceBoardTabView(controller: tabController)),
            ],
          ),
        );
      },
    );
  }

  Future openSearchPage(BuildContext context) {
    if (tab == PriceBoardTabViewEnum.bond) {
      return stockCommonNavigator.openBondSearchPage(context);
    }

    return priceBoardNavigator.openSearchPage(
      context,
      args: SearchArgs(
        marketCodes: [
          MarketCode.HOSE,
          MarketCode.HNX,
          MarketCode.UPCOM,
          MarketCode.FU,
        ],
        itemAction: SearchItemAction.openDetail,
      ),
    );
  }

  @override
  void onEnterForeground() {
    if (mounted) setState(() => key++);

    super.onEnterForeground();
  }

  @override
  void dispose() {
    tabController.removeListener(tabListener);
    tabController.dispose();

    super.dispose();
  }
}

extension NavigationExtensions on BuildContext {
  void safePop() {
    // If there is only one route on the stack, navigate to the initial
    // page instead of popping.
    if (canPop()) {
      pop();
    } else {
      go('/mainTabbar');
    }
  }
}
