import 'package:flutter/material.dart';

import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

part 'home_stock_chart_state.dart';

class StockDetailChartCubit extends AppCubit<HomeStockChartState> {
  StockDetailChartCubit(this.chartData, {required this.stock})
    : super(HomeStockChartState());

  final StockInfoModel stock;

  final List<ChartData> chartData;

  DateTime get now => DateTime.now();

  final duration = const Duration(minutes: 5);

  StockIntradayChartHelper? intradayChartHelper;

  void init() async {
    try {
      chartData.removeBetween(
        TimeOfDay(hour: 11, minute: 30),
        TimeOfDay(hour: 13, minute: 0),
      );

      intradayChartHelper?.dispose();

      intradayChartHelper =
          StockIntradayChartHelper(
              symbol: stock.symbol,
              duration: duration,
              onNewData: (data) {
                emit(state.copyWith(chartData: data));
              },
              refPrice: stock.refPrice!.toDouble(),
              marketCode: stock.marketCode,
            )
            ..setData(chartData)
            ..subscribe();

      emit(state.copyWith(chartData: [...chartData]));
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  @override
  Future<void> close() {
    intradayChartHelper?.dispose();
    return super.close();
  }
}
