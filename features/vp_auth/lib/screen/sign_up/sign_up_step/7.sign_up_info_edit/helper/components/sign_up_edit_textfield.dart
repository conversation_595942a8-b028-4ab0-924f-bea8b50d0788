import 'package:app/common/extensions/color_exts.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/7.sign_up_info_edit/helper/sign_up_info_edit_validator.dart';
import 'package:core_ui/core_ui.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class SignUpEditInfoTextField extends StatefulWidget {
  const SignUpEditInfoTextField({
    Key? key,
    this.hintText,
    this.maxLenght,
    required this.textController,
    this.typeInput,
    required this.onChanged,
    required this.typeValidator,
    this.enable = false,
  }) : super(key: key);

  final String? hintText;
  final int? maxLenght;
  final TextEditingController textController;
  final TextInputType? typeInput;
  final Function() onChanged;
  final SignUpInfoEditTypeValid typeValidator;
  final bool? enable;

  @override
  State<SignUpEditInfoTextField> createState() =>
      _SignUpEditInfoTextFieldState();
}

class _SignUpEditInfoTextFieldState extends State<SignUpEditInfoTextField> {
  bool isSuccess = true;
  String message = '';

  @override
  void initState() {
    super.initState();
    listenerTextField();
  }

  @override
  void dispose() {
    super.dispose();
    widget.textController.dispose();
  }

  void listenerTextField() {
    widget.textController.addListener((() {
      EasyDebounce.debounce(
          'TxtPhoneNumber', const Duration(milliseconds: 1000), () {
        if (!mounted) {
          return;
        }
        validatorText(widget.typeValidator);
      });
    }));
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          child: TextField(
            enabled: widget.enable,
            maxLines: null,
            textAlignVertical: TextAlignVertical.bottom,
            onChanged: (value) {
              widget.onChanged();
              setState(() {
                isSuccess = true;
                message = '';
              });
            },
            autofocus: false,
            cursorColor: ColorUtils.black,
            controller: widget.textController,
            inputFormatters: [
              LengthLimitingTextInputFormatter(widget.maxLenght),
            ],
            style: vpTextStyle.body14?.copyWith(
              color:
                  widget.enable == true ? ColorUtils.black : ColorUtils.gray500,
            ),
            keyboardType: widget.typeInput,
            decoration: const InputDecoration()
                .applyDefaults(Theme.of(context).inputDecorationTheme)
                .copyWith(
                  fillColor: getColorBackground(),
                  filled: true,
                  errorMaxLines: 10,
                  contentPadding: const EdgeInsets.only(bottom: 16, left: 16),
                  focusedBorder: OutlineInputBorder(
                      borderRadius: const BorderRadius.all(
                          Radius.circular(SizeUtils.kRadius8)),
                      borderSide: BorderSide(width: 1, color: getColor())),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                        color: message.isEmpty
                            ? ColorUtils.borderDisable
                            : ColorUtils.red),
                    borderRadius: BorderRadius.circular(SizeUtils.kRadius8),
                  ),
                  disabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: getColor()),
                      borderRadius: BorderRadius.circular(SizeUtils.kRadius8)),
                  hintText: widget.hintText,
                ),
          ),
        ),
        message.isEmpty ? const SizedBox.shrink() : const SizedBox(height: 8),
        message.isEmpty
            ? const SizedBox.shrink()
            : Padding(
                padding: const EdgeInsets.only(left: 16),
                child: Text(message,
                    style: vpTextStyle.body14?
                        .copyWith(color: getColor())),
              )
      ],
    );
  }

  Color getColor() {
    return (widget.enable == false)
        ? ColorUtils.transparent
        : isSuccess
            ? ColorUtils.gray700
            : ColorUtils.red;
  }

  Color getColorBackground() {
    return (widget.enable ?? false)
        ? isSuccess
            ? Colors.transparent
            : ColorExts.backgroundRed
        : ColorUtils.highlightBg;
  }

  /// Validattor
  void validatorText(SignUpInfoEditTypeValid type) {
    bool valid = false;
    String messageValid = '';
    switch (widget.typeValidator) {
      case SignUpInfoEditTypeValid.fullName:
        valid =
            SignUpInfoEditValidator.validFullName(widget.textController.text)
                .isEmpty;
        messageValid =
            SignUpInfoEditValidator.validFullName(widget.textController.text);
        break;
      case SignUpInfoEditTypeValid.identyCard:
        valid = SignUpInfoEditValidator.validIdentifyCard(
                widget.textController.text)
            .isEmpty;
        messageValid = SignUpInfoEditValidator.validIdentifyCard(
            widget.textController.text);
        break;
      case SignUpInfoEditTypeValid.issuePlace:
        valid =
            SignUpInfoEditValidator.validIssuePlace(widget.textController.text)
                .isEmpty;
        messageValid =
            SignUpInfoEditValidator.validIssuePlace(widget.textController.text);
        break;
      case SignUpInfoEditTypeValid.permanentAddress:
        valid = SignUpInfoEditValidator.validPermanentAddress(
                widget.textController.text)
            .isEmpty;
        messageValid = SignUpInfoEditValidator.validPermanentAddress(
            widget.textController.text);
        break;
      case SignUpInfoEditTypeValid.currentAddress:
        valid = SignUpInfoEditValidator.validCurrentAddress(
                widget.textController.text)
            .isEmpty;
        messageValid = SignUpInfoEditValidator.validCurrentAddress(
            widget.textController.text);
        break;
    }

    setState(() {
      isSuccess = valid;
      message = valid ? '' : messageValid;
    });
  }
}
