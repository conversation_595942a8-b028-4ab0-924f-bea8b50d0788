import 'package:app/common/utils/app_config_utils.dart';
import 'package:app/common/widgets/app_pincode_widget.dart';
import 'package:app/common/widgets/constains.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/4.sign_up_supporter/components/supporter_item_select_view.dart';
import 'package:core_ui/core_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'sign_up_supporter_bloc.dart';

class SignUpSupporterPage extends StatelessWidget {
  const SignUpSupporterPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => SignUpSupporterBloc(),
      child: const _BodySignUpSupporter(),
    );
  }
}

class _BodySignUpSupporter extends StatefulWidget {
  const _BodySignUpSupporter({Key? key}) : super(key: key);

  @override
  State<_BodySignUpSupporter> createState() => _BodySignUpEmailState();
}

class _BodySignUpEmailState extends State<_BodySignUpSupporter> {
  @override
  Widget build(BuildContext context) {
    final bloc = context.read<SignUpSupporterBloc>();
    return Container(
      color: ColorUtils.bgMain,
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
          child: BlocBuilder<SignUpSupporterBloc, SignUpSupporterState>(
        builder: (context, state) {
          return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SupportItemSelectView(
                  isSelected: state.type == SignUpSupporterType.had,
                  title: getAccountLang(AccountKeyLang.haveSupport),
                  onCallBack: () {
                    bloc.emitType(SignUpSupporterType.had);
                  },
                ),
                Visibility(
                    visible: state.type == SignUpSupporterType.had,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        buildTxtSupporter(),
                        state.responseText?.valid == null
                            ? const SizedBox()
                            : _BuildResponseText(
                                valid: state.responseText?.valid ?? false,
                                title: state.responseText?.text ?? '',
                              ),
                      ],
                    )),
                kSpacingHeight12,
                Text(getAccountLang(AccountKeyLang.questionSupport),
                    style: vpTextStyle.body14?
                        .copyWith(color: ColorUtils.gray500)),
                kSpacingHeight12,
                SupportItemSelectView(
                  isSelected: state.type == SignUpSupporterType.notYet,
                  title: getAccountLang(AccountKeyLang.dontHaveSupport),
                  onCallBack: () {
                    bloc.emitType(SignUpSupporterType.notYet);
                  },
                ),
                kSpacingHeight10,
                SupportItemSelectView(
                  isSelected: state.type == SignUpSupporterType.noNeed,
                  title: getAccountLang(AccountKeyLang.noNeedSupport),
                  onCallBack: () {
                    bloc.emitType(SignUpSupporterType.noNeed);
                  },
                )
              ]);
        },
      )),
    );
  }

  Widget buildTxtSupporter() {
    final bloc = context.read<SignUpSupporterBloc>();
    return Padding(
      padding: const EdgeInsets.only(top: 12, bottom: 8),
      child: SizedBox(
        height: 40,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(width: SizeUtils.kRadius16),
            Text(AppConfigUtils.preAccNum,
                style: TextStyleUtils.text16Weight600
                    .copyWith(color: ColorUtils.gray500)),
            const SizedBox(width: 16),
            Expanded(
              child: AppPincodeWidget(
                autoDisposeControllers: false,
                controller: bloc.txtSupporter,
                autoHideKeyBoard: false,
                ishideKeyBoard: true,
                textInputFormatter: [
                  FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9]'))
                ],
                count: AppConfigUtils.appOTP,
                onChanged: (value, count) {
                  bloc.onChangeTextFieldSupporter(value);
                },
                onCompleted: (value) {},
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _BuildResponseText extends StatelessWidget {
  final String title;
  final bool valid;

  const _BuildResponseText({Key? key, required this.title, required this.valid})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return !valid
        ? Text(
            title.isNotEmpty
                ? title
                : getAccountLang(AccountKeyLang.notFoundSupport),
            style: vpTextStyle.body14?.copyWith(
              color: ColorUtils.red,
            ),
          )
        : Text.rich(TextSpan(children: [
            TextSpan(
              text: '${getAccountLang(AccountKeyLang.presenter)}: ',
              style: vpTextStyle.body14?,
            ),
            TextSpan(
              text: title,
              style: vpTextStyle.body14?.copyWith(
                color: ColorUtils.primary,
              ),
            )
          ]));
  }
}
