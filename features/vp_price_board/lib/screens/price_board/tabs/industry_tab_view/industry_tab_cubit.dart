import 'package:flutter/cupertino.dart';
import 'package:vp_common/extensions/list_extensions.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_price_board/core/repository/price_board_repository.dart';
import 'package:vp_price_board/model/enum/industry_level.dart';
import 'package:vp_price_board/model/industry_model.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

part 'industry_tab_state.dart';

class IndustryTabCubit extends AppCubit<IndustryTabState>
    with StockInfoSocketMixin {
  IndustryTabCubit({required this.repository})
    : super(IndustryTabState(apiStatus: ApiStatus.initial()));

  final PriceBoardRepository repository;

  Future loadData() async {
    await _getIndustries([IndustryLevel.icbLevel2]);

    if (state.industries.isNullOrEmpty) return;

    emit(state.copyWith(industrySelected: state.industries!.first));

    await _getStockByIndustry();
  }

  Future _getIndustries(List<IndustryLevel> levels) async {
    try {
      emit(state.copyWith(apiStatus: ApiStatus.loading()));

      final data = await repository.getIndustries(levels);

      emit(state.copyWith(industries: data));
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      emit(state.copyWith(apiStatus: ApiStatus.error(e)));
    }
  }

  Future _getStockByIndustry() async {
    try {
      unsubscribeStockInfo();

      final industry = state.industrySelected!;

      emit(state.copyWith(apiStatus: ApiStatus.loading()));

      final data = await repository.getStockInfoByIndustry(industry.code);

      subscribeStockInfo(data?.map((e) => e.symbol).toSet());

      emit(state.copyWith(apiStatus: ApiStatus.done(), stocks: data));
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      emit(state.copyWith(apiStatus: ApiStatus.error(e)));
    }
  }

  void onIndustryChanged(IndustryModel industry) {
    if (industry == state.industrySelected) return;

    emit(state.copyWith(industrySelected: industry));

    _getStockByIndustry();
  }

  void sortBy(SortMode mode) {
    emit(state.copyWith(sortMode: mode));
  }

  Future onRefresh() {
    if (state.industrySelected == null) {
      return loadData();
    }

    return _getStockByIndustry();
  }

  @override
  Future<void> close() {
    unsubscribeStockInfo();
    return super.close();
  }
}
