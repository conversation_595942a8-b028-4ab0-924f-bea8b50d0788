import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:vp_auth/generated/l10n.dart';
import 'package:vp_common/vp_common.dart';

class SignUpUtils {
  static const male = '001';
  static const feMale = '002';

  static String getGenderString(String? gender) {
    return gender == SignUpUtils.feMale
        ? S.current.account_gender_female
        : S.current.account_gender_male;
  }
}

class SignUpPermissionUtils {
  /* Check Permission Camera - File */
  static bool checkingPermission = false;

  static Future<bool> checkPermissionCamera() async {
    try {
      if (!checkingPermission) {
        SignUpPermissionUtils.checkingPermission = true;

        final permissions = <Permission>[Permission.camera];

        if (Platform.isIOS) {
          permissions.add(Permission.storage);
        }

        Map<Permission, PermissionStatus> status = await permissions.request();

        final listStatus = status.values;

        final isHaveDenied = listStatus.contains(PermissionStatus.denied) ||
            listStatus.contains(PermissionStatus.permanentlyDenied);

        dlog('isHaveDenied: $isHaveDenied');
        if (isHaveDenied) {
          openAppSettings();
        }
        return !isHaveDenied;
      }
      return false;
    } catch (e, stackTrace) {
      return false;
    } finally {
      SignUpPermissionUtils.checkingPermission = false;
    }
  }

  /* Check Permission Camera - File */
  static Future<bool> checkPermissionFile() async {
    if (!SignUpPermissionUtils.checkingPermission) {
      try {
        SignUpPermissionUtils.checkingPermission = true;

        Permission permission = Permission.storage;

        if (Platform.isAndroid) {
          final deviceInfo = await DeviceInfoPlugin().androidInfo;

          final sdkInt = deviceInfo.version.sdkInt ?? 0;

          permission = sdkInt < 33 ? Permission.storage : Permission.photos;
        } else {
          permission = Permission.storage;
        }

        final status = await permission.request();

        final isHaveDenied = status == PermissionStatus.denied ||
            status == PermissionStatus.permanentlyDenied;

        if (isHaveDenied) {
          openAppSettings();
        }

        return !isHaveDenied;
      } catch (e, stackTrace) {
        return false;
      } finally {
        SignUpPermissionUtils.checkingPermission = false;
      }
    }
    return false;
  }
}
