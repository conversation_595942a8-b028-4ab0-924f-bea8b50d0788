import 'package:app/common/widgets/container_hepler.dart';
import 'package:app/common/widgets/vpbank_loading.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:core_ui/utils/color_utils.dart';
import 'package:core_ui/utils/size_utils.dart';
import 'package:core_ui/utils/text_utils.dart';
import 'package:flutter/material.dart';

class FptLoadingOcrEkyc extends StatelessWidget {
  const FptLoadingOcrEkyc({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: ContainerHelper.decorationBottomAll(),
      child: IntrinsicHeight(
        child: Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
          const SizedBox(height: 24),
          Text(getAccountLang(AccountKeyLang.loadingWait),
              style: vpTextStyle.body14?
                  .copyWith(color: ColorUtils.black)),
          const SizedBox(height: 8),
          const VPBankLoading(),
        ]),
      ),
    );
  }
}
