import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/7.sign_up_info_edit/helper/components/sign_up_edit_date_picker.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/7.sign_up_info_edit/helper/components/sign_up_edit_textfield.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/7.sign_up_info_edit/helper/components/sign_up_gender_widget.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/7.sign_up_info_edit/helper/sign_up_info_edit_validator.dart';
import 'package:core_ui/utils/color_utils.dart';
import 'package:core_ui/utils/size_utils.dart';
import 'package:core_ui/utils/text_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'sign_up_info_edit_cubit.dart';

class SignUpInfoEditPage extends StatelessWidget {
  const SignUpInfoEditPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => SignUpInfoEditCubit(),
      child: _BodySignUpInfoEdit(),
    );
  }
}

class _BodySignUpInfoEdit extends StatefulWidget {
  @override
  State<_BodySignUpInfoEdit> createState() => _BodySignUpInfoEditState();
}

class _BodySignUpInfoEditState extends State<_BodySignUpInfoEdit> {
  @override
  Widget build(BuildContext context) {
    final obj = SignUpData().ocrData;
    final bloc = context.read<SignUpInfoEditCubit>();
    return ColoredBox(
      color: ColorUtils.bgMain,
      child: Scrollbar(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),
                    // Ho va ten
                    _TitleTextFiled(
                        title: getAccountLang(AccountKeyLang.fullName)),
                    buildTextField(
                        SignUpInfoEditTypeValid.fullName, bloc.txtFullName),

                    // Sinh nhat
                    _TitleTextFiled(
                        title: getAccountLang(AccountKeyLang.birthDay)),
                    const SizedBox(height: 8),
                    SignUpDatePicker(
                        enable: false,
                        strDate: obj.dateofbirth,
                        pickerDate: (pickerDate) {
                          obj.dateofbirth = pickerDate;
                        }),

                    // Gioi tinh
                    _TitleTextFiled(
                        title: getAccountLang(AccountKeyLang.gender),
                        isRequired: true),
                  ],
                ),
              ),
              SignUpGenderWidget(
                currentId: bloc.getGender(),
                onSelectGender: (gender) {
                  bloc.selectGender = gender;
                  bloc.setEnable(true);
                },
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // So chung minh thu
                    _TitleTextFiled(
                        title: getAccountLang(AccountKeyLang.identifyCard)),
                    buildTextField(SignUpInfoEditTypeValid.identyCard,
                        bloc.txtIdentifyCard,
                        maxLenght: 20),

                    // Ngay cap chung minh thu
                    _TitleTextFiled(
                        title: getAccountLang(AccountKeyLang.issuedDateCard)),
                    const SizedBox(height: 8),
                    SignUpDatePicker(
                        enable: false,
                        strDate: obj.idDate,
                        pickerDate: (pickerDate) {
                          obj.idDate = pickerDate;
                        }),

                    //Noi cap chung minh thu
                    _TitleTextFiled(
                        title:
                            getAccountLang(AccountKeyLang.placeOfIssuedCard)),
                    buildTextField(SignUpInfoEditTypeValid.issuePlace,
                        bloc.txtPlaceOfIssuedCard),

                    // Dia chi thuong tru
                    _TitleTextFiled(
                        title: getAccountLang(AccountKeyLang.permanentAddress)),
                    buildTextField(SignUpInfoEditTypeValid.permanentAddress,
                        bloc.txtPermanentAddress),

                    // Dia chi noi o hien tai
                    _TitleTextFiled(
                        title: getAccountLang(AccountKeyLang.currentAdd),
                        isRequired: false),
                    buildTextField(
                      SignUpInfoEditTypeValid.currentAddress,
                      bloc.txtCurrentAddress,
                      maxLenght: 1000,
                      enable: true,
                      hintText: getAccountLang(AccountKeyLang.typeAddress),
                    ),
                  ],
                ),
              ),
              // // Cong dan hoa ky
              // buildTitleEdit(
              //     getAccountLang( AccountKeyLang.americaGuide)),
              // AccountAmericaWidget(
              //     isAmerica: obj.america,
              //     america: (value) {
              //       bloc.isAmerica = value;
              //     }),
              const SizedBox(height: 58),
            ],
          ),
        ),
      ),
    );
  }

  // Build textfield
  Widget buildTextField(
      SignUpInfoEditTypeValid type, TextEditingController controller,
      {bool enable = false, int maxLenght = 255, String? hintText}) {
    return Padding(
        padding: const EdgeInsets.only(top: 8, right: 8),
        child: SignUpEditInfoTextField(
          enable: enable,
          hintText: hintText,
          typeInput: type == SignUpInfoEditTypeValid.identyCard
              ? TextInputType.number
              : TextInputType.text,
          maxLenght: maxLenght,
          onChanged: () {
            context.read<SignUpInfoEditCubit>().setEnable(true);
          },
          textController: controller,
          typeValidator: type,
        ));
  }
}

class _TitleTextFiled extends StatelessWidget {
  final String title;
  final bool isRequired;

  const _TitleTextFiled({Key? key, required this.title, this.isRequired = true})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 16),
      child: Wrap(
        children: [
          Text(title,
              style: TextStyleUtils.text14Weight600
                  .copyWith(color: ColorUtils.black),
              textAlign: TextAlign.start),
          isRequired
              ? Text('*',
                  style: TextStyleUtils.text18Weight400
                      .copyWith(color: ColorUtils.red))
              : const SizedBox.shrink()
        ],
      ),
    );
  }
}
