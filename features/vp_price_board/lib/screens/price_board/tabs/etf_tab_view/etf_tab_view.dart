import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_price_board/screens/price_board/tabs/etf_tab_view/etf_tab_cubit.dart';
import 'package:vp_price_board/widget/sort/normal_sort_view.dart';
import 'package:vp_price_board/widget/sort/stock_board_sort_view.dart';
import 'package:vp_price_board/widget/stock_list_item.dart';
import 'package:vp_price_board/widget/stock_list_item_builder_view.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class EtfTabView extends StatefulWidget {
  const EtfTabView({super.key});

  @override
  State<EtfTabView> createState() => _EtfTabViewState();
}

class _EtfTabViewState extends State<EtfTabView>
    with AutomaticKeepAliveClientMixin {
  ScrollController controller = ScrollController();

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return MultiBlocProvider(
      providers: [BlocProvider(create: (_) => EtfTabCubit()..loadData())],
      child: BlocBuilder<EtfTabCubit, EtfTabState>(
        builder: (_, state) {
          return StockListItemBuilderView(
            apiStatus: state.apiStatus,
            data: state.items,
            onRetry: () => onRefresh(context),
            builder: (context, data) {
              return StockBoardSortView(
                onChanged: onSort,
                sortType: state.sortType,
                status: state.sortStatus,
                builder: (context, columns) {
                  return StockListView(
                    items: data,
                    controller: controller,
                    columns: columns,
                    onRefresh: () => onRefresh(context),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }

  Future onRefresh(BuildContext context) =>
      context.read<EtfTabCubit>().onRefresh();

  void onSort(BuildContext context, SortMode mode) {
    context.read<EtfTabCubit>().sortBy(mode);

    controller.jumpTo(0);
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;
}
