import 'dart:async';
import 'dart:io';

import 'package:app/common/error/show_error.dart';
import 'package:app/common/utils/app_api/base_response.dart';
import 'package:app/common/utils/app_error_code.dart';
import 'package:app/common/utils/app_log_utils.dart';
import 'package:app/common/utils/app_snackbar_utils.dart';
import 'package:app/common/widgets/vpbank_loading.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/models/otp_verification_obj.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/onboarding_repository.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/cubit/sign_up_step.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_tracking.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/i_sign_up_step.dart';
import 'package:app/packages/shared/modules/auth/common/lang/auth_lang.dart';
import 'package:app/packages/shared/modules/auth/common/lang/auth_lang_key.dart';
import 'package:core/di/injector.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:sms_autofill/sms_autofill.dart';

part 'sign_up_otp_state.dart';

class SignUpOtpBloc extends ISignUpController<SignUpOtpState> {
  SignUpOtpBloc(this.sessionID) : super(const SignUpOtpState());

  final String? sessionID;

  @override
  String? get title => getAuthLang(AuthLangKey.inputOTP);

  @override
  String? get subTitle => getAuthLang(AuthLangKey.verify);

  @override
  int? get progress => SignUpStep.phone.process;

  @override
  void init() {
    if (sessionID is String) {
      emit(state.copyWith(sessionId: sessionID));
    }

    emitShowTimer(true);

    if (Platform.isAndroid) {
      initSmsListener();
    }
  }

  @override
  bool isTypeScreen(SignUpTypeScreen value) {
    return value == SignUpTypeScreen.otp;
  }

  @override
  void onNext(SignUpTypeScreen type) {
    if (isTypeScreen(type)) {
      apiVerifyOTP();
    }
  }

  void _loading(bool value) {
    emit(state.copyWith(isLoading: value));
  }

  TextEditingController txtPincode = TextEditingController();
  int _countCheckWrongOTP = 0;

  void apiVerifyOTP() async {
    try {
      showDialogLoading();
      final param = OtpVerificationRequestObj(
          phone: SignUpData().mobilePhone, otp: txtPincode.text);
      final value = await sl.get<OnboardingRepository>().otpVerification(param);
      if (value != null) {
        if (value.isSuccess()) {
          SignUpRouter().onPushReplace(SignUpTypeScreen.email);
          SignUpTracking().signupOtpSuccess();
        } else {
          emitMsgErr(value.message ?? '');
          _countCheckWrongOTP++;
          if (_countCheckWrongOTP == 6) {
            apiGenOTP();
          }
          SignUpTracking().signupOtpFail(value.message);
        }
      }
    } catch (e) {
      SignUpTracking().signupOtpFail(e);
      showError(e);
    } finally {
      hideDialogLoading();
    }
    setEnable(true);
  }

  void apiGenOTP() async {
    OnboardingResponse? baseResponse;
    txtPincode.clear();
    _loading(true);
    emit(state.copyWith(messErr: ''));
    emitShowTimer(false);
    try {
      baseResponse = await sl.get<OnboardingRepository>().noAuthUserOtP(
          phoneNumber: SignUpData().mobilePhone, sessionId: state.sessionId);
      _loading(false);
      if (baseResponse != null) {
        if (baseResponse.isSuccess()) {
          _countCheckWrongOTP = 0;
          emitShowTimer(true);
          if (Platform.isAndroid) {
            initSmsListener();
          }
        } else {
          if (baseResponse.code == AppErrorCode.eIABERR14) {
            emitMsgErr(baseResponse.message ?? '');
            return;
          }
          showMessage(baseResponse.message);
        }
      }
    } catch (e) {
      _loading(false);
      showError(e);
    }
  }

  void emitMsgErr(String value) {
    emit(state.copyWith(messErr: value));
  }

  void emitShowTimer(bool value) {
    emit(state.copyWith(isShowTimer: value));
  }

  void initSmsListener() async {
    await SmsAutoFill().unregisterListener();
    await SmsAutoFill().listenForCode();
    SmsAutoFill().code.listen((event) {
      txtPincode.text = event;
    });
  }

  @override
  Future<void> close() {
    try {
      SmsAutoFill().unregisterListener();
      txtPincode.dispose();
    } catch (e) {
      dlog(e.toString());
    }
    return super.close();
  }
}
