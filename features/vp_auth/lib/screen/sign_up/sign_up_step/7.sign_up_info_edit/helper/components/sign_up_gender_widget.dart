import 'package:app/common/extensions/color_exts.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_utils.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_ui/utils/text_utils.dart';
import 'package:flutter/material.dart';

class SignUpGenderWidget extends StatefulWidget {
  const SignUpGenderWidget(
      {Key? key, this.currentId = 1, required this.onSelectGender})
      : super(key: key);
  final int currentId;
  final Function(String) onSelectGender;
  @override
  State<SignUpGenderWidget> createState() => _SignUpGenderWidgetState();
}

class _SignUpGenderWidgetState extends State<SignUpGenderWidget> {
  int id = 1;
  @override
  void initState() {
    super.initState();
    id = widget.currentId;
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: <Widget>[
        const SizedBox(width: 2),
        Radio(
          value: 1,
          groupValue: id,
          activeColor: ColorUtils.primary,
          onChanged: (val) {
            setState(() {
              id = 1;
              widget.onSelectGender(SignUpUtils.male);
            });
          },
        ),
        Text(
          getAccountLang(AccountKeyLang.genderMale),
          style: TextStyleUtils.text16Weight400,
        ),
        Radio(
          value: 2,
          groupValue: id,
          activeColor: ColorUtils.primary,
          onChanged: (val) {
            setState(() {
              id = 2;
              widget.onSelectGender(SignUpUtils.feMale);
            });
          },
        ),
        Text(
          getAccountLang(AccountKeyLang.genderFeMale),
          style: TextStyleUtils.text16Weight400,
        ),
      ],
    );
  }
}
