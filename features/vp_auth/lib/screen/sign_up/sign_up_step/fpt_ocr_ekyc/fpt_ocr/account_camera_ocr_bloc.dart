import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:app/common/utils/app_log_utils.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/fpt_ocr_ekyc/fpt_ocr/sign_up_base_stream.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';

import '../fpt_helper/fpt_handle_image.dart';

class AccountCameraOcrBloc {
  // Stream UI
  final controllerStateUI = StreamController<AccountCameraOcrBlocState>();

  Stream<AccountCameraOcrBlocState> get streamStateUI =>
      controllerStateUI.stream;

  // Stream ListerData
  final controllerData = StreamController();

  Stream get streamData => controllerData.stream;

  Future<void> saveImageIDCard(
      AccountTypeIDCard typeImageIDCard, String pathXFile) async {
    final bytes = await File(pathXFile).readAsBytes();
    final img.Image? capturedImage = img.decodeImage(bytes);

    if (capturedImage != null) {
      final img.Image orientedImage =
          img.bakeOrientation(capturedImage); /* Xử lý xoay lại ảnh */

      /* Crop ảnh */
      var cropSize = min(orientedImage.width - 20, orientedImage.height);
      int offsetX = 10;
      int heightY = 600;
      int offsetY = (orientedImage.height ~/ 2) - (heightY * 0.75).toInt();

      img.Image destImage = img.copyCrop(
        orientedImage,
        x: offsetX,
        y: offsetY,
        width: cropSize,
        height: heightY,
      );
      /* Crop ảnh */

      Directory appDocDir = await getApplicationDocumentsDirectory();
      String appDocPath = appDocDir.path;
      String pathSave =
          '$appDocPath/IDCARD_${typeImageIDCard.name}_${DateTime.now().microsecondsSinceEpoch}${FptHandleImage.jpg}';

      await FptHandleImage.deleteFile(
          pathXFile); /*  Xoá file đã lưu vào từ camera */

      await File(pathSave).writeAsBytes(img.encodeJpg(destImage, quality: 95));
      dlog('File OCR Save: $pathSave'); /* Lưu ảnh vào trong bộ nhớ */

      emitSuccess(pathSave);
    }
  }

  void emitSuccess(String pathSave) {
    controllerData.sink
        .add(SignUpBaseStreamObj<String>(message: '', data: pathSave));
  }

  void emitError(String errorMsg) {
    controllerData.sink.add(SignUpBaseStreamObj<String>(message: errorMsg));
  }

  void sinkState(AccountCameraOcrBlocState state) {
    if (!controllerStateUI.isClosed) {
      controllerStateUI.sink.add(state);
    }
  }

  void dispose() {
    controllerStateUI.close();
    controllerData.close();
  }
}

enum AccountCameraOcrBlocState { loading, hideLoading }

enum AccountTypeIDCard { front, back }
