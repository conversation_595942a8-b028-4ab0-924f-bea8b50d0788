import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_price_board/model/enum/bond_type.dart';
import 'package:vp_price_board/screens/price_board/tabs/bond_tab_view/bond_category_cubit.dart';
import 'package:vp_price_board/widget/sort/bond_sort_view.dart';
import 'package:vp_price_board/widget/stock_board_bond_item_view.dart';
import 'package:vp_price_board/widget/stock_list_item_builder_view.dart';

class BondCategoryView extends StatefulWidget {
  const BondCategoryView({required this.bondType, super.key});

  final BondType bondType;

  @override
  State<BondCategoryView> createState() => _BondCategoryViewState();
}

class _BondCategoryViewState extends State<BondCategoryView>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocProvider(
      create:
          (_) => BondCategoryTabCubit(bondType: widget.bondType)..loadData(),
      child: BlocBuilder<BondCategoryTabCubit, BondCategoryTabState>(
        builder: (_, state) {
          return StockListItemBuilderView(
            apiStatus: state.apiStatus,
            data: state.sortedData,
            onRetry: () => onRefresh(context),
            builder: (context, data) {
              return BondSortView(
                onChanged:
                    (_, mode) =>
                        context.read<BondCategoryTabCubit>().sortBy(mode),
                sortType: state.sortMode?.sortType,
                status: state.sortMode?.status,
                builder: (context, columns) {
                  return StockListBondItem(
                    items: data,
                    columns: columns,
                    onRefresh: () => onRefresh(context),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }

  Future onRefresh(BuildContext context) =>
      context.read<BondCategoryTabCubit>().onRefresh();

  @override
  bool get wantKeepAlive => true;
}
