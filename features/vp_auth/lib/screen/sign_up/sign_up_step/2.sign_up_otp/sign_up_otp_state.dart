part of 'sign_up_otp_bloc.dart';

class SignUpOtpState extends Equatable {
  final bool isLoading;
  final String messErr;
  final bool isShowTimer;
  final String sessionId;
  const SignUpOtpState(
      {this.isLoading = false,
      this.messErr = '',
      this.isShowTimer = false,
      this.sessionId = ''});
  @override
  List<Object?> get props => [isLoading, messErr, isShowTimer, sessionId];

  SignUpOtpState copyWith(
      {bool? isLoading,
      String? messErr,
      bool? isShowTimer,
      String? sessionId}) {
    return SignUpOtpState(
        sessionId: sessionId ?? this.sessionId,
        isShowTimer: isShowTimer ?? this.isShowTimer,
        isLoading: isLoading ?? this.isLoading,
        messErr: messErr ?? this.messErr);
  }
}
