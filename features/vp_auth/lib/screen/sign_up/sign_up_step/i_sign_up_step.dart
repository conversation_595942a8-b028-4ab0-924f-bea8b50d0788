import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/sign_up_flow.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/sign_up_router.dart';

abstract class ISignUpController<T> extends Cubit<T> {
  ISignUpController(super.initialState) {
    init();

    onResume();

    _listenResumeStream();

    _listenOnNextStream();
  }

  StreamSubscription? _subscription;

  StreamSubscription? _onNextSubscription;

  void _listenResumeStream() {
    _subscription = SignUpFlow()
        .onResumeStream
        .where((event) => isTypeScreen(event))
        .listen((event) => onResume());
  }

  void _listenOnNextStream() {
    _onNextSubscription = SignUpFlow()
        .onNextStream
        .where((event) => isTypeScreen(event))
        .listen((event) => onNext(event));
  }

  void showLoading() => SignUpFlow().emitLoading(true);

  void hideLoading() => SignUpFlow().emitLoading(false);

  void init() {}

  void setEnable(bool value) {
    enable = value;
    SignUpFlow().emitEnable(value, title: buttonNextContent);
  }

  @mustCallSuper
  void onResume() {
    SignUpFlow().emitEnable(enable);

    if (title != null) SignUpFlow().emitTitle(title!, subTitle: subTitle);

    if (progress != null) SignUpFlow().emitProcess(progress!);
  }

  void onNext(SignUpTypeScreen type);

  bool isTypeScreen(SignUpTypeScreen value);

  @override
  Future<void> close() async {
    _subscription?.cancel();
    _onNextSubscription?.cancel();
    super.close();
  }

  late bool enable = false;

  String? get title => null;

  String? get subTitle => null;

  String? get buttonNextContent => null;

  int? get progress => null;
}
