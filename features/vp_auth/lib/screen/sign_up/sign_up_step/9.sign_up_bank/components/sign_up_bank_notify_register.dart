import 'dart:async';

import 'package:app/common/assets/common_key_assets.dart';
import 'package:app/global/navigator/app_router.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_ui/widgets/design_system/dialog/notifty_dialog.dart';
import 'package:flutter/material.dart';

Future showNotifyRegisterDialog(
  BuildContext context, {
  required Function onOk,
  required GestureTapCallback onViewTransactionCondition,
  required GestureTapCallback onViewContract,
  bool barrierDismissible = true,
}) {
  return showNotifyDialog(
    context: context,
    barrierDismissible: false,
    image: CommonKeyAssets.logoVpBank,
    colorButtonRight: ColorUtils.primary,
    title: getAccountLang(AccountKeyLang.openAnAccount),
    titleStyle: TextStyleUtils.text20Weight700.copyWith(
      color: ColorUtils.gray700,
    ),
    imagePadding: EdgeInsets.only(bottom: 12),
    showCloseIcon: true,
    iconSize: 100,
    textButtonRight: getAccountLang(AccountKeyLang.confirm),
    colorBorderButtonLeft: ColorUtils.gray700,
    contentWidget: Column(
      children: [
        _ItemText(
          havePoint: false,
          text: 'Bằng việc nhấn “Xác nhận”, Tôi đồng ý:',
        ),
        _ItemText(
          text:
              'Đồng ý cho VPBankS cung cấp các thông tin cần thiết cho VPBank để mở tài khoản ngân hàng.',
        ),
        InkWell(
          onTap: onViewTransactionCondition,
          child: _ItemText(
            textWidget: Text.rich(TextSpan(children: [
              TextSpan(
                text: 'Đồng ý với ',
                style: vpTextStyle.body14?.copyWith(
                  color: ColorUtils.gray700,
                ),
              ),
              TextSpan(
                text: 'Điều kiện giao dịch chung',
                style: vpTextStyle.body14?.copyWith(
                  color: ColorUtils.primary,
                ),
              ),
              TextSpan(
                text: ' về cung cấp và sử dụng dịch vụ tại VPBank.',
                style: vpTextStyle.body14?.copyWith(
                  color: ColorUtils.gray700,
                ),
              )
            ])),
          ),
        ),
        InkWell(
          onTap: onViewContract,
          child: _ItemText(
            textWidget: Text.rich(TextSpan(children: [
              TextSpan(
                text: 'Đồng ý cho VPBank xử lý dữ liệu cá nhân của Tôi theo ',
                style: vpTextStyle.body14?.copyWith(
                  color: ColorUtils.gray700,
                ),
              ),
              TextSpan(
                text: 'Văn bản xác nhận này',
                style: vpTextStyle.body14?.copyWith(
                  color: ColorUtils.primary,
                ),
              ),
            ])),
          ),
        ),
      ],
    ),
    onPressedRight: () {
      navigation.goBack();
      Timer(const Duration(milliseconds: 200), () {
        onOk();
      });
    },
  );
}

class _ItemText extends StatelessWidget {
  final Widget? textWidget;
  final String? text;
  final bool havePoint;

  const _ItemText({
    Key? key,
    this.textWidget,
    this.text,
    this.havePoint = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        havePoint
            ? Container(
                width: 3,
                height: 3,
                margin:
                    const EdgeInsets.symmetric(horizontal: 6).copyWith(top: 10),
                decoration: BoxDecoration(
                  color: ColorUtils.gray700,
                  shape: BoxShape.circle,
                ),
              )
            : const SizedBox(),
        Expanded(
            child: textWidget ??
                Text(
                  text ?? '',
                  style: vpTextStyle.body14?.copyWith(
                    color: ColorUtils.gray700,
                  ),
                ))
      ],
    );
  }
}
