import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/sign_up_flow.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:vp_core/vp_core.dart';

import 'sign_up_state.dart';

class SignUpBloc extends BaseCubit<SignUpState> {
  late StreamSubscription _onEnableSubcription;
  late StreamSubscription _titleSubcription;
  late StreamSubscription _processSubcription;

  SignUpBloc() : super(SignUpStateInitial()) {
    SignUpRouter().init();
    SignUpFlow().init();
    emit(SignUpStateEnableButton(enable: false));
    emit(SignUpStateTitle(title: ''));
    _onListenerFromStep();
  }

  void _onListenerFromStep() {
    _onEnableSubcription =
        SignUpFlow().enableStream.listen((ArgumentEnableBtnNext event) {
      emit(SignUpStateEnableButton(enable: event.enable, title: event.title));
    });
    _titleSubcription = SignUpFlow().titleStream.listen((event) {
      emit(SignUpStateTitle(
        title: event.title,
        subTitle: event.subTitle,
      ));
    });
    _processSubcription = SignUpFlow().processStream.listen((event) {
      emit(SignUpStateProcess(process: event));
    });
  }

  void emitOnNext() {
    SignUpFlow().emitOnNext(SignUpRouter().currentScreen);
  }

  @override
  Future<void> close() {
    _onEnableSubcription.cancel();
    _titleSubcription.cancel();
    _processSubcription.cancel();
    SignUpFlow().dispose();
    SignUpRouter().onClear();
    return super.close();
  }
}
