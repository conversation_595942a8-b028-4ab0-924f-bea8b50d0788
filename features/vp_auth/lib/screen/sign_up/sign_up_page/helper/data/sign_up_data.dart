
import 'package:vp_auth/model/sign_up/model_ekyc_fis/ocr_ekyc.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/data/sign_up_attach_data.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/data/sign_up_bank_data.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/data/sign_up_ocr_data.dart';
import 'package:vp_common/vp_common.dart';

class SignUpData {
  static final SignUpData _singleton = SignUpData._internal();

  factory SignUpData() => _singleton;

  SignUpData._internal();

  SignUpAttachData attachDataObj = SignUpAttachData();
  SignUpOcrData ocrData = SignUpOcrData();
  SignUpBankData bankData = SignUpBankData();
  OCR dataOCR = OCR();

  int nfcFail = 0;

  bool _isComboWithBank = false;

  bool get isComboWithBank => _isComboWithBank;

  setComboWithBank(bool value) {
    _isComboWithBank = value;
  }

  bool _bankSuccess = false;

  bool get bankSuccess => _bankSuccess;

  setBankSuccess(bool value) {
    _bankSuccess = value;
  }

  String _mobilePhone = '';

  String get mobilePhone => _mobilePhone;

  setMobilePhone(String value) {
    _mobilePhone = value;
  }

  String _email = '';

  String get email => _email;

  setEmail(String email) {
    _email = email;
  }

  String? _afChanel;

  void setAfChanel(String? value) {
    _afChanel = value;
  }

  String? get afChanel => _afChanel;

  String _refcustodycd = '';

  String get refcustodycd => _refcustodycd;
  bool agreeOpenBankAccount = false;

  void setRefcustodycd(String value) {
    _refcustodycd = value;
  }

  bool isPre116C() {
    return _refcustodycd
        .toLowerCase()
        .startsWith(AppConfigUtils.preAccNum.toLowerCase());
  }

  void clear({bool clearRrefcustodycd = false}) {
    attachDataObj.clear();
    ocrData.clear();
    dataOCR = OCR();
    bankData.clear();
    _mobilePhone = '';
    _email = '';
    nfcFail = 0;
    _bankSuccess = false;

    if (clearRrefcustodycd) {
      _refcustodycd = '';
      _afChanel = null;
    }
  }
}
