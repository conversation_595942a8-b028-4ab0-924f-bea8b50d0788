import 'package:flutter/cupertino.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_price_board/model/paging_and_sorting_mixin.dart';

import 'package:vp_stock_common/vp_stock_common.dart';

part 'stock_index_state.dart';

class StockIndexBloc extends AppCubit<StockIndexState>
    with StockInfoSocketMixin {
  StockIndexBloc({StockCommonRepository? repository, required this.indexCode})
    : repository = repository ?? GetIt.instance.get<StockCommonRepository>(),
      super(StockIndexState(apiStatus: ApiStatus.initial()));

  final StockCommonRepository repository;

  final IndexCode indexCode;

  Future loadData() async {
    try {
      emit(state.copyWith(apiStatus: ApiStatus.loading()));

      unsubscribeStockInfo();

      final data = await repository.getStocksByIndex(indexCode: indexCode);

      subscribeStockInfo(data?.content?.map((e) => e.symbol).toSet());

      emit(state.copyWith(apiStatus: ApiStatus.done(), paging: data));
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      emit(state.copyWith(apiStatus: ApiStatus.error(e)));
    }
  }

  Future onRefresh() => loadData();

  void sortBy(SortMode mode) {
    emit(state.copyWith(sortMode: mode));
  }

  @override
  Future<void> close() {
    unsubscribeStockInfo();
    return super.close();
  }
}
