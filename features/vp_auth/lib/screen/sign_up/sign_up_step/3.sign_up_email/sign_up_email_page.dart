import 'package:app/common/widgets/constains.dart';
import 'package:app/packages/shared/modules/account/common/assets/account_key_assets.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/3.sign_up_email/widgets/sign_up_email_text_guild.dart';
import 'package:app/packages/shared/modules/money/money_presentation/feature/money_cash_in/view/components/vpbank_loading_money.dart';
import 'package:core_ui/utils/color_utils.dart';
import 'package:core_ui/utils/size_utils.dart';
import 'package:core_ui/utils/text_utils.dart';
import 'package:core_ui/widgets/design_system/textfield/app_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'sign_up_email_bloc.dart';

class SignUpEmailPage extends StatelessWidget {
  const SignUpEmailPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => SignUpEmailBloc(),
      child: const _BodySignUpEmail(),
    );
  }
}

class _BodySignUpEmail extends StatefulWidget {
  const _BodySignUpEmail({Key? key}) : super(key: key);

  @override
  State<_BodySignUpEmail> createState() => _BodySignUpEmailState();
}

class _BodySignUpEmailState extends State<_BodySignUpEmail> {
  @override
  Widget build(BuildContext context) {
    final bloc = context.read<SignUpEmailBloc>();
    return Container(
      color: ColorUtils.bgMain,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(getAccountLang(AccountKeyLang.enterEmail),
                  style: TextStyleUtils.text16Weight600
                      .copyWith(color: ColorUtils.black)),
              kSpacingHeight8,
              BlocBuilder<SignUpEmailBloc, SignUpEmailState>(
                builder: (context, state) {
                  return AppTextField(
                      autofocus: false,
                      typeInput: TextInputType.emailAddress,
                      prefixPathSVG: AccountKeyAssets.email,
                      maxLength: 255,
                      enableBorderColor: ColorUtils.gray100,
                      hintText: getAccountLang(AccountKeyLang.emailRegister),
                      textController: bloc.txtEmail,
                      messsage: state.msgTextField,
                      isSuccess: state.validTextfield,
                      readOnly: state.isReadOnly,
                      errorStyle: vpTextStyle.body14?.copyWith(
                        color: ColorUtils.red,
                      ),
                      onChanged: (value) {});
                },
              ),
              const SignUpEmailTextGuild(),
              kSpacingHeight24,
              _Loading()
            ],
          ),
        ),
      ),
    );
  }
}

class _Loading extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SignUpEmailBloc, SignUpEmailState>(
      buildWhen: (previous, current) => previous.isLoading != current.isLoading,
      builder: (context, state) {
        return state.isLoading
            ? const Center(child: VPBankLoading())
            : const SizedBox.shrink();
      },
    );
  }
}
