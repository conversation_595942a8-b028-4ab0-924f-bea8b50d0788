import 'package:core_ui/utils/color_utils.dart';
import 'package:core_ui/utils/size_utils.dart';
import 'package:core_ui/utils/text_utils.dart';
import 'package:flutter/material.dart';

class SignUpInfoContentView extends StatelessWidget {
  const SignUpInfoContentView({
    Key? key,
    required this.title,
    required this.content,
  }) : super(key: key);
  final String title;
  final String content;

  @override
  Widget build(BuildContext context) {
    return getContentInfo(title, content);
  }

  Padding getContentInfo(String title, String content) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
                child: Text(
              title,
              style: vpTextStyle.body14?
                  .copyWith(color: ColorUtils.gray700),
              textAlign: TextAlign.start,
            )),
            const SizedBox(width: SizeUtils.kRadius16),
            Expanded(
                child: Text(content,
                    style: TextStyleUtils.text14Weight500
                        .copyWith(color: ColorUtils.black),
                    textAlign: TextAlign.end)),
          ]),
    );
  }
}
