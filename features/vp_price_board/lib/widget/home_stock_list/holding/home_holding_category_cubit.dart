import 'package:flutter/cupertino.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_price_board/model/paging_and_sorting_mixin.dart';
import 'package:vp_stock_common/model/enum/chart_period.dart';
import 'package:vp_stock_common/model/price_chart_model.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

part 'home_holding_category_state.dart';

class HomeHoldingCategoryCubit extends AppCubit<HomeHoldingCategoryState>
    with StockInfoSocketMixin {
  HomeHoldingCategoryCubit()
    : super(HomeHoldingCategoryState(apiStatus: ApiStatus.initial()));

  final StockCommonRepository stockCommonRepository =
      GetIt.instance.get<StockCommonRepository>();

  final StockPortfolioRepository stockPortfolioRepository =
      GetIt.instance.get<StockPortfolioRepository>();

  final ChartRepository chartRepository = GetIt.instance.get<ChartRepository>();

  Future loadData() async {
    try {
      emit(state.copyWith(apiStatus: ApiStatus.loading()));

      final symbols = await stockPortfolioRepository.getSymbols();

      final data =
          symbols.isNullOrEmpty
              ? null
              : await Future.wait([
                stockCommonRepository.getStockInfoV2(symbols: symbols),
                getChartData(symbols),
              ]);

      subscribeStockInfo(symbols?.toSet());

      emit(
        state.copyWith(
          apiStatus: ApiStatus.done(),
          chartData: data?.lastOrNull as Map<String, List<ChartData>>?,
          paging: data?.firstOrNull as BasePagingResponse<StockInfoModel>?,
        ),
      );
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      emit(state.copyWith(apiStatus: ApiStatus.error(e)));
    }
  }

  Future<Map<String, List<ChartData>>?> getChartData(
    List<String>? symbols,
  ) async {
    try {
      final chartData = await chartRepository.getChartData(
        symbols: symbols!,
        period: ChartPeriod.fiveMinutes,
      );

      return chartData;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }

    return null;
  }

  Future onRefresh() => loadData();

  @override
  Future<void> close() {
    unsubscribeStockInfo();
    return super.close();
  }
}
