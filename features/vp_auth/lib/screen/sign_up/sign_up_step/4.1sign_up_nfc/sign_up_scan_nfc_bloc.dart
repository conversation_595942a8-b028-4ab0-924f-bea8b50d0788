import 'dart:async';
import 'dart:io';

import 'package:app/common/error/show_error.dart';
import 'package:app/common/extensions/string_extensions.dart';
import 'package:app/common/utils/app_device_id.dart';
import 'package:app/common/utils/app_log_utils.dart';
import 'package:app/common/utils/loading_utils.dart';
import 'package:app/global/navigator/app_router.dart';
import 'package:app/global/session/app_session.dart';
import 'package:app/packages/fund/common/extensions/string_extension.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/onboarding_repository.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/cubit/sign_up_step.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/4.1sign_up_nfc/sign_up_scan_nfc_page.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/4.1sign_up_nfc/widget/popup_error.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/i_sign_up_step.dart';
import 'package:app/packages/shared/modules/partner_connection/data/model/read_nfc_request.dart';
import 'package:app/packages/shared/modules/partner_connection/data/model/save_log_nfc_request.dart';
import 'package:app/packages/shared/modules/partner_connection/presentation/feature/open_account/bloc/open_account_bloc_cubit.dart';
import 'package:core/di/injector.dart';
import 'package:flutter/material.dart';
import 'package:nfc_plugin_flutter/nfc_plugin_flutter.dart';

part 'sign_up_scan_nfc_state.dart';

class SignUpScanNFCBloc extends ISignUpController<SignUpScanNFCState> {
  SignUpScanNFCBloc() : super(const SignUpScanNFCState());

  late SignUpScanNfcRequest _scanNfcData;

  SignUpScanNfcRequest get _scanNfcRequest => SignUpScanNfcRequest(
        id: SignUpData().dataOCR.soCmt ?? '',
        dob: (SignUpData().dataOCR.namSinh ?? '').convertDateFormat(),
        doe: ((SignUpData().dataOCR.ngayHetHan ?? '').isValidDateFormat
                ? (SignUpData().dataOCR.ngayHetHan ?? '')
                : '31/12/2099')
            .convertDateFormat(),
        doi: (SignUpData().dataOCR.ngayCap ?? '').convertDateFormat(),
        idDate: SignUpData().dataOCR.ngayCap ?? '',
        idTypeNew: SignUpData().dataOCR.loaiCmt,
      );

  @override
  String? get title => 'Quét thông tin chip CCCD';

  @override
  int? get progress => SignUpStep.nfc.process;

  @override
  void init() {
    _scanNfcData = _scanNfcRequest;
    _goToNFC(_scanNfcData, navigation.navigatorKey.currentContext!);
  }

  @override
  void onResume() {
    super.onResume();
    if (SignUpData().nfcFail == 2 || SignUpData().bankSuccess) {
      setEnable(true);
    }
  }

  @override
  bool isTypeScreen(SignUpTypeScreen value) {
    return value == SignUpTypeScreen.nfc;
  }

  @override
  void onNext(SignUpTypeScreen type) {
    if (isTypeScreen(type)) {
      SignUpRouter().onPush(SignUpTypeScreen.infoOcr);
    }
  }

  Future<void> _goToNFC(SignUpScanNfcRequest data, BuildContext context) async {
    dynamic res = await SampleCallNativeFlutter.nfcScan(
        data.id, data.dob, data.doe, data.doi, 'vi');
    SaveLogNFCRequest logNFCRequest = SaveLogNFCRequest(
      mobile: Session().iamUserInfo?.mobile,
      accountNo: Session().iamUserInfo?.accountNo,
      deviceName: await AppUtils.getDeviceName(),
      status: "FAIL",
    );
    String messageCommon =
        'Vui lòng ấn “Tiếp tục” để mở tài khoản chứng khoán của VPBankS';
    String titleContinue = 'Không thể quét NFC hiện tại';
    String titleTryAgain = 'Quét NFC không thành công';
    if (res['error'] != null) {
      switch (res['error']) {
        case "WRONG_CITIZEN_ID_CARD":
        case "InvalidMRZKey":
          if (SignUpData().nfcFail == 2) {
            logicShowErrorWhenNFCFailTheThree(
                logNFCRequest, titleContinue, messageCommon, context);
            return;
          }
          String message =
              'Thông tin chip CCCD của bạn đang không khớp với ảnh chụp. Vui lòng chụp lại';
          String title = 'Thông tin không khớp';
          popupError(context,
              messageError: message,
              title: title,
              textButton: "Thử lại sau", onBack: () async {
            await Future.delayed(const Duration(milliseconds: 200));
            SignUpData().attachDataObj.clear();
            SignUpRouter().onBackUtil(SignUpTypeScreen.supporter);
            SignUpRouter().onPush(SignUpTypeScreen.upload);
          });
          SignUpData().nfcFail++;
          logNFCRequest.message = title;
          await sl
              .get<OnboardingRepository>()
              .saveNFCLog(request: logNFCRequest);
          return;
        case "ERROR_CODE_UN_SUPPORT_NFC":
        case "NFCNotSupported":
          String message = 'Thiết bị không hỗ trợ đọc NFC';
          await _showPopupErrorContinue(
              context, titleContinue, message, logNFCRequest);
          return;
        case "CANNOT_OPEN_DEVICE":
          String message =
              'Thiết bị có hỗ trợ NFC nhưng không thể kết nối đến chức năng NFC của hệ thống';
          await _showPopupErrorContinue(
              context, titleContinue, message, logNFCRequest);
          return;
        case "NFC_IS_OFF":
          String message = 'Chức năng NFC bị tắt';
          await _showPopupErrorContinue(
              context, titleContinue, message, logNFCRequest);
          return;
        case "ERROR_CODE_UN_SUPPORT_API_VERSION":
          String message = 'Lỗi hệ điều hành không được hỗ trợ';
          await _showPopupErrorContinue(
              context, titleContinue, message, logNFCRequest);
          return;
        case "ERROR_TIME_OUT":
        case "ERROR_CODE_TIME_OUT":
        case "Timeout":
          if (SignUpData().nfcFail == 2) {
            logicShowErrorWhenNFCFailTheThree(
                logNFCRequest, titleContinue, messageCommon, context);
            return;
          }
          String message = 'Hết thời gian chờ';
          await _showPopupErrorTryAgain(
              context, titleTryAgain, message, logNFCRequest);
          return;
        case "CARD_LOST_CONNECTION":
        case "ConnectionError":
          if (SignUpData().nfcFail == 2) {
            logicShowErrorWhenNFCFailTheThree(
                logNFCRequest, titleContinue, messageCommon, context);
            return;
          }
          String message =
              'Thẻ bị đưa ra ngoài vùng đọc của NFC hoặc mất kết nối đến thẻ (lỗi hệ thống)';
          await _showPopupErrorTryAgain(
              context, titleTryAgain, message, logNFCRequest);
          return;
        case "UNKNOWN":
        case "ResponseError":
        case "ERROR_CODE_USER_CANCELED":
        case "UserCanceled":
        case "Tag response error / no response":
        case "Tag is not connected":
        case "Tag connection lost":
        case "Session invalidated":
          if (SignUpData().nfcFail == 2) {
            logicShowErrorWhenNFCFailTheThree(
                logNFCRequest, titleContinue, messageCommon, context);
            return;
          }
          String message = 'Vui lòng giữ yên vị trí thẻ và thử lại';
          await _showPopupErrorTryAgain(
              context, titleTryAgain, message, logNFCRequest);
          return;
        case "UnexpectedError":
          if (SignUpData().nfcFail == 2) {
            logicShowErrorWhenNFCFailTheThree(
                logNFCRequest, titleContinue, messageCommon, context);
            return;
          }
          String message =
              'Lỗi xảy ra khi không ngắt 2 phiên đọc NFC cách nhau 1.5s';
          await _showPopupErrorTryAgain(
              context, titleTryAgain, message, logNFCRequest);
          return;
        default:
          if (SignUpData().nfcFail == 2) {
            logicShowErrorWhenNFCFailTheThree(
                logNFCRequest, titleContinue, messageCommon, context);
            return;
          }
          popupError(context,
              messageError: res['error'],
              onBack: () => SignUpRouter().onBack());
          SignUpData().nfcFail++;
          logNFCRequest.message = res['error'];
          await sl
              .get<OnboardingRepository>()
              .saveNFCLog(request: logNFCRequest);
          return;
      }
    }
    logNFCRequest.status = 'SUSSCESS';
    await sl.get<OnboardingRepository>().saveNFCLog(request: logNFCRequest);
    LoadingUtil.showLoading();
    ReadNFCRequest nfcRequest = Platform.isAndroid
        ? ReadNFCRequest(
            aAResult: res['aAResult'],
            challenge: res['challenge'],
            dg1: res['dg1'],
            dg2: res['dg2'],
            dg3: res['dg3'],
            dg4: res['dg4'],
            dg5: res['dg5'],
            dg6: res['dg6'],
            dg7: res['dg7'],
            dg8: res['dg8'],
            dg9: res['dg9'],
            dg10: res['dg10'],
            dg11: res['dg11'],
            dg12: res['dg12'],
            dg13: res['dg13'],
            dg14: res['dg14'],
            dg15: res['dg15'],
            dg16: res['dg16'],
            eACCAResult: res['eACCAResult'],
            sod: res['sod'],
            idDate: data.idDate,
            idTypeNew: data.idTypeNew)
        : ReadNFCRequest(
            aAResult: res['data']['aAResult'],
            challenge: res['data']['challenge'],
            dg1: res['data']['dg1'],
            dg2: res['data']['dg2'],
            dg3: res['data']['dg3'],
            dg4: res['data']['dg4'],
            dg5: res['data']['dg5'],
            dg6: res['data']['dg6'],
            dg7: res['data']['dg7'],
            dg8: res['data']['dg8'],
            dg9: res['data']['dg9'],
            dg10: res['data']['dg10'],
            dg11: res['data']['dg11'],
            dg12: res['data']['dg12'],
            dg13: res['data']['dg13'],
            dg14: res['data']['dg14'],
            dg15: res['data']['dg15'],
            dg16: res['data']['dg16'],
            eACCAResult: res['data']['eACCAResult'],
            sod: res['data']['sod'],
            idDate: data.idDate,
            idTypeNew: data.idTypeNew);
    try {
      var data = await sl
          .get<OnboardingRepository>()
          .readNFCExternal(nfcRequest: nfcRequest);
      if (data.fullName != null) {
        try {
          final data =
              await sl.get<OnboardingRepository>().checkVpbankAccountExist();
          if (data.code == EXISTENCE.IABNOT671.name && data.isSuccess()) {
            SignUpData().setBankSuccess(true);
            SignUpData().setComboWithBank(true);
          }
        } catch (e) {
          dlog(e);
        }
      }
      LoadingUtil.hideLoading();
      SignUpData().nfcFail = 0;
      SignUpRouter().onPush(SignUpTypeScreen.infoOcr);
    } catch (e) {
      LoadingUtil.hideLoading();
      if (SignUpData().nfcFail == 2) {
        logicShowErrorWhenNFCFailTheThree(
            logNFCRequest, titleContinue, messageCommon, context);
        return;
      }
      final message = await getErrorMessage(e);
      await _showPopupErrorTryAgain(
          context, titleTryAgain, message ?? '', logNFCRequest);
    }
  }

  Future<void> _showPopupErrorTryAgain(BuildContext context, String title,
      String message, SaveLogNFCRequest logNFCRequest) async {
    popupError(context,
        messageError: message,
        title: title,
        textButton: "Thử lại",
        onBack: () =>
            _goToNFC(_scanNfcData, navigation.navigatorKey.currentContext!));
    SignUpData().nfcFail++;
    logNFCRequest.message = message;
    await sl.get<OnboardingRepository>().saveNFCLog(request: logNFCRequest);
  }

  Future<void> _showPopupErrorContinue(BuildContext context, String title,
      String message, SaveLogNFCRequest logNFCRequest) async {
    popupError(context,
        messageError: message,
        title: title,
        textButton: "Tiếp tục",
        onBack: () => SignUpRouter().onPush(SignUpTypeScreen.infoOcr));
    logNFCRequest.message = title;
    await sl.get<OnboardingRepository>().saveNFCLog(request: logNFCRequest);
  }

  void logicShowErrorWhenNFCFailTheThree(SaveLogNFCRequest logNFCRequest,
      String titleCommon, String messageCommon, BuildContext context) async {
    popupError(context,
        messageError: messageCommon,
        title: titleCommon,
        textButton: "Tiếp tục",
        onBack: () => SignUpRouter().onPush(SignUpTypeScreen.infoOcr));
    logNFCRequest.message = titleCommon;
    await sl.get<OnboardingRepository>().saveNFCLog(request: logNFCRequest);
  }
}
