import 'package:vp_core/vp_core.dart';
import 'package:vp_socket/vp_socket.dart';
import 'package:vp_stock_common/helper/stock_info_socket_mixin.dart';
import 'package:vp_trading/core/repository/holding_portfolio_repository.dart';
import 'package:vp_trading/model/holding_portfolio/holding_portfolio_stock_model.dart';

import 'list_holding_portfolio_state.dart';

class ListHoldingPortfolioCubit extends Cubit<ListHoldingPortfolioState>
    with StockInfoSocketMixin {
  final HoldingPortfolioRepository _repository =
      GetIt.instance<HoldingPortfolioRepository>();

  ListHoldingPortfolioCubit()
    : super(
        ListHoldingPortfolioState(
          apiStatus: ApiStatus.initial(),
          symbol: "ALL",
        ),
      );

  /// Extract symbols từ portfolioList để subscribe socket
  Set<String> extractSymbols() {
    try {
      final symbols =
          state.portfolioList
              .where((item) => item.symbol != null && item.symbol!.isNotEmpty)
              .map((item) => item.symbol!)
              .toSet();

      print('Extracted symbols for socket subscription: $symbols');
      return symbols;
    } catch (e) {
      print('Error extracting symbols: $e');
      return <String>{};
    }
  }

  void updateSubAccount(SubAccountModel? subAccount) {
    try {
      // Unsubscribe khi thay đổi filter
      unsubscribeStockInfo();
    } catch (e) {
      print('Error unsubscribing on subAccount update: $e');
    }
    emit(state.copyWith(subAccount: subAccount));
    fetchHoldingPortfolio();
  }

  void updateSymbol(String? symbol) {
    try {
      // Unsubscribe khi thay đổi filter
      unsubscribeStockInfo();
    } catch (e) {
      print('Error unsubscribing on symbol update: $e');
    }
    emit(state.copyWith(symbol: symbol));
    fetchHoldingPortfolio();
  }

  Future<void> fetchHoldingPortfolio() async {
    // Unsubscribe trước khi fetch data mới
    try {
      unsubscribeStockInfo();
    } catch (e) {
      print('Error unsubscribing: $e');
    }

    emit(state.copyWith(apiStatus: ApiStatus.loading()));
    try {
      var accountId = "";
      if (state.subAccount?.accountType != SubAccountType.all) {
        accountId = state.subAccount?.id ?? "";
      }
      final List<HoldingPortfolioStockModel>? portfolioList = await _repository
          .getHoldingPortfolio(
            accountId: accountId,
            symbol: state.symbol ?? "ALL",
          );

      // gộp các item có cùng symbol
      final mergedList = <HoldingPortfolioStockModel>[];
      final tempList = List<HoldingPortfolioStockModel>.from(
        portfolioList ?? [],
      );

      while (tempList.isNotEmpty) {
        final currentSymbol = tempList.first.customSymbol;
        final merged = mergeHoldingsBySymbol(tempList, currentSymbol);
        mergedList.addAll(merged);

        // Loại bỏ các phần tử đã được gộp khỏi tempList
        tempList.removeWhere((item) => item.customSymbol == currentSymbol);
      }
      mergedList.sort((a, b) => (a.symbol ?? '').compareTo(b.symbol ?? ''));
      emit(
        state.copyWith(apiStatus: ApiStatus.done(), portfolioList: mergedList),
      );

      // Subscribe socket sau khi emit success state
      try {
        final symbols = extractSymbols();
        if (symbols.isNotEmpty) {
          subscribeStockInfo(symbols);
          print(
            'Successfully subscribed to socket for ${symbols.length} symbols',
          );
        } else {
          print('No symbols to subscribe - portfolio is empty');
        }
      } catch (e) {
        print('Error subscribing to socket: $e');
        // Fallback: continue without socket subscription
      }
    } catch (e) {
      print('Error fetching portfolio: $e');
      emit(state.copyWith(apiStatus: ApiStatus.error(e)));
    }
  }

  List<HoldingPortfolioStockModel> mergeHoldingsBySymbol(
    List<HoldingPortfolioStockModel> originalList,
    String symbol,
  ) {
    final filtered =
        originalList.where((e) => e.customSymbol == symbol).toList();
    if (filtered.isEmpty) return [];

    // Tính tổng
    num total = 0;
    double costPriceValue = 0;
    double costPriceAmt = 0;
    num blocked = 0;
    num restrict = 0;

    for (var item in filtered) {
      total += item.total ?? 0;
      costPriceValue += item.costPriceValue;
      costPriceAmt += item.costPriceAmt ?? 0;
      blocked += item.blocked ?? 0;
      restrict += item.restrict ?? 0;
    }

    // Tìm item có symbol chứa "_WFT"
    final wftItem = filtered.firstWhere(
      (item) => item.symbol?.contains('_WFT') == true,
      orElse: () => filtered.first,
    );

    // Tìm item không có "_WFT" trong symbol để làm base
    final baseItem = filtered.firstWhere(
      (item) => item.symbol?.contains('_WFT') != true,
      orElse: () => filtered.first,
    );

    // kiểm tra một symbol có tồn tại trong 2 tiểu khoản
    final isSymbolInBothSubAccounts =
        filtered.where((item) {
          return item.accountId != baseItem.accountId;
        }).isNotEmpty;

    final merged = baseItem.copyWith(
      symbol: symbol,
      total: total,
      costPrice: costPriceValue / total,
      costPriceAmt: costPriceAmt,
      receivingRight: wftItem.receivingRight,
      blocked: blocked,
      withdraw: wftItem.withdraw,
      isMergeSymbol: filtered.length > 1,
      isSymbolInBothSubAccounts: isSymbolInBothSubAccounts,
      restrict: restrict,
    );

    return [merged];
  }

  // Giá trị thị trường = Tổng giá trị thị trường của tất cả các CP.
  //Giá trị hiện tại của danh mục = SUM(Giá trị hiện tại của mã)

  num get marketValueTotal {
    return state.portfolioList.fold(0, (a, b) => a + b.marketValue);
  }

  //  Gốc đầu tư = SUM(Giá trị vốn của mỗi cổ phiếu) .Trong đó:
  // Giá trị vốn của mỗi cổ phiếu = Giá vốn của cổ phiếu * Khối lượng cổ phiếu
  num get capitalValue {
    return state.portfolioList.fold(
      0,
      (a, b) => a + (b.costPrice ?? 0) * (b.total ?? 0),
    );
  }

  // Lãi/lô dự kiến = giá trị thị trường - gốc đầu tư.
  num get profit {
    return marketValueTotal - capitalValue;
  }

  // % lãi/lỗ = Lãi/lỗ dự kiến / Gốc đầu tư * 100%
  double get profitPercent {
    if (capitalValue == 0) return 0;
    return (profit / capitalValue) * 100;
  }

  @override
  void onSocketStockInfoListener(VPStockInfoData data) {
    try {
      // Validate socket data
      if (data.symbol == null || data.closePrice == null) {
        print(
          'Invalid socket data: symbol=${data.symbol}, closePrice=${data.closePrice}',
        );
        return;
      }

      bool hasChanges = false;
      final updatedList =
          state.portfolioList.map((item) {
            // Handle case khi symbol trong portfolio không match với socket data
            if (item.symbol == data.symbol ||
                item.customSymbol == data.symbol) {
              // Chỉ update nếu giá thực sự thay đổi
              if (item.currentPrice != data.closePrice) {
                hasChanges = true;
                print(
                  'Price updated for ${data.symbol}: ${item.currentPrice} -> ${data.closePrice}',
                );
                return item.copyWith(currentPrice: data.closePrice);
              }
            }
            return item;
          }).toList();

      // Chỉ emit state khi có thay đổi thực sự
      if (hasChanges) {
        emit(state.copyWith(portfolioList: updatedList));
      }
    } catch (e, stackTrace) {
      print('Error processing socket data: $e');
      print('StackTrace: $stackTrace');
      // Graceful degradation - không throw error để không crash app
    }
  }

  @override
  Future<void> close() {
    // Cleanup socket khi dispose cubit
    unsubscribeStockInfo();
    return super.close();
  }
}
