import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/error_view.dart';
import 'package:vp_design_system/custom_widget/error_widget.dart';
import 'package:vp_design_system/custom_widget/no_data_view.dart';
import 'package:vp_price_board/widget/stock_shimmer_list_item.dart';

class StockListItemBuilderView<T> extends StatelessWidget {
  const StockListItemBuilderView({
    required this.builder,
    required this.apiStatus,
    required this.data,
    this.onRetry,
    super.key,
  });

  final ApiStatus apiStatus;

  final List<T>? data;

  final VoidCallback? onRetry;

  final Widget Function(BuildContext context, List<T> data) builder;

  @override
  Widget build(BuildContext context) {
    if (apiStatus.isLoading) {
      return const StockShimmerListItem();
    }

    if (apiStatus.isError) {
      return  ErrorView(
        showErrorImage: true,
        onTryAgain: onRetry,
      );
    }

    if (apiStatus.isDone) {
      if (data.hasData) {
        return builder.call(context, data!);
      }

      return const NoDataView();
    }

    return const SizedBox.shrink();
  }
}
