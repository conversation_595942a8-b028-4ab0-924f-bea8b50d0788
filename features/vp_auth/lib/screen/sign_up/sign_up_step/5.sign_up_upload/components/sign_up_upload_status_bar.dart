import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:core_ui/core_ui.dart';
import 'package:flutter/material.dart';

class SignUpUploadStatusBar extends StatefulWidget {
  final Function(int index) onChangeTab;
  final int initPage;

  const SignUpUploadStatusBar({
    Key? key,
    required this.onChangeTab,
    required this.initPage,
  }) : super(key: key);

  @override
  State<SignUpUploadStatusBar> createState() => _SignUpUploadStatusBarState();
}

class _SignUpUploadStatusBarState extends State<SignUpUploadStatusBar> {
  int _indexSelected = 0;

  @override
  void initState() {
    super.initState();
    _indexSelected = widget.initPage;
  }

  void _onItemTap(int index) {
    widget.onChangeTab(index);
    setState(() {
      _indexSelected = index;
    });
  }

  @override
  void didUpdateWidget(covariant SignUpUploadStatusBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initPage != widget.initPage) {
      _indexSelected = widget.initPage;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
            child: _Item(
          text: getAccountLang(AccountKeyLang.font),
          onTap: _onItemTap,
          index: 0,
          indexSelected: _indexSelected,
        )),
        Expanded(
            child: _Item(
          text: getAccountLang(AccountKeyLang.behind),
          onTap: _onItemTap,
          index: 1,
          indexSelected: _indexSelected,
        ))
      ],
    );
  }
}

class _Item extends StatelessWidget {
  final String text;
  final Function(int index) onTap;
  final int indexSelected;
  final int index;

  const _Item(
      {Key? key,
      required this.text,
      required this.onTap,
      required this.indexSelected,
      required this.index})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    bool selected = indexSelected == index;
    return InkWell(
      onTap: () {
        onTap(index);
      },
      child: Container(
        height: 46,
        width: double.infinity,
        decoration: BoxDecoration(
          border: Border(
              bottom: BorderSide(
            color: selected ? ColorUtils.primary : ColorUtils.gray100,
            width: selected ? 2 : 1,
          )),
        ),
        child: Center(
          child: Text(
            text,
            style: vpTextStyle.body14?.copyWith(
              color: selected ? ColorUtils.primary : ColorUtils.gray700,
              fontWeight: selected ? FontWeight.w600 : FontWeight.w400,
            ),
          ),
        ),
      ),
    );
  }
}
