import 'package:flutter/cupertino.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

part 'future_category_state.dart';

class FutureCategoryTabCubit extends AppCubit<FutureCategoryTabState>
    with StockInfoSocketMixin {
  FutureCategoryTabCubit({required this.stockType})
    : super(FutureCategoryTabState(apiStatus: ApiStatus.initial()));

  final StockCommonRepository repository = GetIt.instance.get();

  final StockType stockType;

  Future<MarketInfoModel?> getMarketInfo() async {
    try {
      return await repository.getMarketInfo(
        stockType == StockType.FUGB ? IndexCode.VN100 : IndexCode.VN30,
      );
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }

    return null;
  }

  Future loadData() async {
    try {
      emit(state.copyWith(apiStatus: ApiStatus.loading()));

      unsubscribeStockInfo();

      final data = await Future.wait([
        getMarketInfo(),
        repository.getFuStockDetail(stockType: stockType),
      ]);

      final marketInfo = data.firstOrNull as MarketInfoModel?;

      final fu = data.lastOrNull as BasePagingResponse<StockInfoModel>?;

      subscribeStockInfo(fu?.content?.map((e) => e.symbol).toSet());

      emit(
        state.copyWith(
          data: fu?.content,
          marketInfo: marketInfo,
          apiStatus: ApiStatus.done(),
        ),
      );
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      emit(state.copyWith(apiStatus: ApiStatus.error(e)));
    }
  }

  Future onRefresh() => loadData();

  void sortBy(SortMode mode) {
    emit(state.copyWith(sortMode: mode));
  }

  @override
  Future<void> close() {
    unsubscribeStockInfo();
    return super.close();
  }
}
