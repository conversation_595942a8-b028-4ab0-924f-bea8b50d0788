import 'package:app/common/assets/common_key_assets.dart';
import 'package:core_ui/core_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../../../../../../../common/widgets/constains.dart';

class ItemDescriptionView extends StatelessWidget {
  const ItemDescriptionView({super.key, this.title});

  final String? title;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 4),
            child: SvgPicture.asset(CommonKeyAssets.icCheck2,
                width: 16, color: ColorUtils.primary),
          ),
          kSpacingWidth8,
          Text(title ?? 'Sử dụng CCCD/CMND bản gốc và còn hiệu lực.',
              softWrap: true,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
              style: TextStyleUtils.text13Weight500
                  .copyWith(color: ColorUtils.gray900),
              textAlign: TextAlign.start),
        ],
      ),
    );
  }
}
