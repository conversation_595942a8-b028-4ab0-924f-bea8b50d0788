import 'package:flutter/cupertino.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_price_board/model/enum/covered_warrant.dart';
import 'package:vp_price_board/model/paging_and_sorting_mixin.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

part 'covered_warrant_category_state.dart';

class CoveredWarrantCategoryCubit extends AppCubit<CoveredWarrantCategoryState>
    with StockInfoSocketMixin {
  CoveredWarrantCategoryCubit({
    StockCommonRepository? repository,
    required this.type,
  }) : repository = repository ?? GetIt.instance.get<StockCommonRepository>(),
       super(CoveredWarrantCategoryState(apiStatus: ApiStatus.initial()));

  final StockCommonRepository repository;

  final CoveredWarrantType? type;

  Future loadData() async {
    try {
      emit(state.copyWith(apiStatus: ApiStatus.loading()));

      unsubscribeStockInfo();

      final data = await repository.getStockInfoV2(
        issuerName: type?.value,
        stockType: StockType.CW,
      );

      subscribeStockInfo(data?.content?.map((e) => e.symbol).toSet());

      emit(state.copyWith(apiStatus: ApiStatus.done(), paging: data));
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      emit(state.copyWith(apiStatus: ApiStatus.error(e)));
    }
  }

  Future onRefresh() => loadData();

  void sortBy(SortMode mode) {
    emit(state.copyWith(sortMode: mode));
  }

  @override
  Future<void> close() {
    unsubscribeStockInfo();
    return super.close();
  }
}
