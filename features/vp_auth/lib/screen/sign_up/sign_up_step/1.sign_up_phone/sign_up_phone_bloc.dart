
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:vp_auth/core/constant/sign_up_type.dart';
import 'package:vp_auth/generated/l10n.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/cubit/sign_up_step.dart';
import 'package:vp_auth/screen/sign_up/sign_up_step/i_sign_up_step.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';

part 'sign_up_phone_state.dart';

class SignUpPhoneBloc extends ISignUpController<SignUpPhoneState> {
  SignUpPhoneBloc()
      : super(
          SignUpPhoneState(
            msgTextField: S.current.account_guide_input_phone,
          ),
        );

  @override
  String? get title => S.current.account_declare_information;

  @override
  int? get progress => SignUpStep.phone.process;

  final phoneKey = GlobalKey<FormState>();

  @override
  bool isTypeScreen(SignUpTypeScreen value) {
    return value == SignUpTypeScreen.phone;
  }

  @override
  void onNext(SignUpTypeScreen type) {
    if (isTypeScreen(type)) {
      AppKeyboardUtils.dismissKeyboard();
      loading(true);
      emit(state.copyWith(phoneNumber: txtPhoneNumber.text));
    }
  }

  void loading(bool value) {
    emit(state.copyWith(isLoading: value));
  }

  TextEditingController txtPhoneNumber = TextEditingController();

  void onChangeTextField(String value) {
    setEnable(false);
    if (txtPhoneNumber.text.isEmpty) {
      StockUtils.validateAndSave(phoneKey);
      emit(state.copyWith(
        validTextfield: SignUpPhoneValidTextFieldType.defaultType,
        msgTextField: S.current.account_guide_input_phone,
      ));
      return;
    }
    final phone = txtPhoneNumber.text.removeAllWhitespace();
    if (validPhone(phone)) {
      StockUtils.validateAndSave(phoneKey);
      emit(state.copyWith(
        validTextfield: SignUpPhoneValidTextFieldType.valid,
        msgTextField: S.current.account_success_phone,
      ));
      setEnable(true);
    }

    EasyDebounce.debounce(
        'TxtPhoneNumber', const Duration(milliseconds: AppConfigUtils.debounce),
        () {
      if (!StockUtils.validateAndSave(phoneKey)) {
        emit(state.copyWith(
            validTextfield: SignUpPhoneValidTextFieldType.invalid));
        return;
      }
      emit(state.copyWith(
        validTextfield: txtPhoneNumber.text.isNotEmpty
            ? SignUpPhoneValidTextFieldType.valid
            : SignUpPhoneValidTextFieldType.defaultType,
        msgTextField: txtPhoneNumber.text.isNotEmpty
            ? S.current.account_success_phone
            : S.current.account_guide_input_phone,
      ));
    });
  }

  String? onValidTexField(String? text) {
    if (!validPhone(txtPhoneNumber.text) && txtPhoneNumber.text.isNotEmpty) {
      return S.current.account_wrong_phone;
    }
    return null;
  }

  bool validPhone(String value) {
    final validStart0 = value.startsWith('0') && value.length == 10;
    final validStart84 = value.startsWith('+84') && value.length == 12;
    final valid = AppValidator.validPhone(value);
    return (validStart0 || validStart84) & valid;
  }

  void verifyRecaptchaEnterprise(
      String recaptchaToken, Function(String sessionID) onCallBack) async {
    OnboardingResponse? baseResponse;
    try {
      baseResponse = await sl.get<OnboardingRepository>().noAuthUserOtP(
          phoneNumber: txtPhoneNumber.text,
          recaptchaToken: recaptchaToken);
      if (baseResponse != null) {
        if (baseResponse.isSuccess()) {
          onCallBack(baseResponse.data['session_id']);
          SignUpData().clear();
        } else {
          showMessage(baseResponse.message);
          loading(false);
        }
      }
    } catch (e) {
      showError(e);
      loading(false);
    }
  }
}
