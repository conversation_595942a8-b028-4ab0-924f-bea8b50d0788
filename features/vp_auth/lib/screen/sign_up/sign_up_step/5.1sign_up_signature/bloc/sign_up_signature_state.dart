part of 'sign_up_signature_bloc.dart';

// @immutable
// abstract class SignUpSignatureState {}
//
// class SignUpSignatureInitial extends SignUpSignatureState {}
class SignUpSignatureState {
  final String pathSignature;

  const SignUpSignatureState({this.pathSignature = ''});

  // @override
  // List<Object?> get props => [pathSignature];

  SignUpSignatureState copyWith({String? pathSignature}) {
    return SignUpSignatureState(
        pathSignature: pathSignature ?? this.pathSignature);
  }
}
