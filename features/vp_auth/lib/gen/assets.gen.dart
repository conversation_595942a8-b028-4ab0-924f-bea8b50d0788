/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/faceid.svg
  String get faceid => 'assets/icons/faceid.svg';

  /// File path: assets/icons/fingerprint.svg
  String get fingerprint => 'assets/icons/fingerprint.svg';

  /// File path: assets/icons/ic_calendar.svg
  String get icCalendar => 'assets/icons/ic_calendar.svg';

  /// File path: assets/icons/ic_capture.svg
  String get icCapture => 'assets/icons/ic_capture.svg';

  /// File path: assets/icons/ic_cccd.svg
  String get icCccd => 'assets/icons/ic_cccd.svg';

  /// File path: assets/icons/ic_close.svg
  String get icClose => 'assets/icons/ic_close.svg';

  /// File path: assets/icons/ic_exchange.svg
  String get icExchange => 'assets/icons/ic_exchange.svg';

  /// File path: assets/icons/ic_forgot_password.svg
  String get icForgotPassword => 'assets/icons/ic_forgot_password.svg';

  /// File path: assets/icons/ic_hide_text.svg
  String get icHideText => 'assets/icons/ic_hide_text.svg';

  /// File path: assets/icons/ic_market.svg
  String get icMarket => 'assets/icons/ic_market.svg';

  /// File path: assets/icons/ic_phone.svg
  String get icPhone => 'assets/icons/ic_phone.svg';

  /// File path: assets/icons/ic_session_expire.svg
  String get icSessionExpire => 'assets/icons/ic_session_expire.svg';

  /// File path: assets/icons/ic_show_text.svg
  String get icShowText => 'assets/icons/ic_show_text.svg';

  /// File path: assets/icons/ic_smart_otp.svg
  String get icSmartOtp => 'assets/icons/ic_smart_otp.svg';

  /// File path: assets/icons/ic_support.svg
  String get icSupport => 'assets/icons/ic_support.svg';

  /// File path: assets/icons/ic_support_ai.svg
  String get icSupportAi => 'assets/icons/ic_support_ai.svg';

  /// File path: assets/icons/ic_support_email.svg
  String get icSupportEmail => 'assets/icons/ic_support_email.svg';

  /// File path: assets/icons/ic_support_web.svg
  String get icSupportWeb => 'assets/icons/ic_support_web.svg';

  /// File path: assets/icons/ic_support_zalo.svg
  String get icSupportZalo => 'assets/icons/ic_support_zalo.svg';

  /// File path: assets/icons/ic_support_zalo_dark_mode.svg
  String get icSupportZaloDarkMode =>
      'assets/icons/ic_support_zalo_dark_mode.svg';

  /// File path: assets/icons/login_error.svg
  String get loginError => 'assets/icons/login_error.svg';

  /// File path: assets/icons/mail.svg
  String get mail => 'assets/icons/mail.svg';

  /// File path: assets/icons/password.svg
  String get password => 'assets/icons/password.svg';

  /// File path: assets/icons/phone.svg
  String get phone => 'assets/icons/phone.svg';

  /// File path: assets/icons/user.svg
  String get user => 'assets/icons/user.svg';

  /// List of all assets
  List<String> get values => [
        faceid,
        fingerprint,
        icCalendar,
        icCapture,
        icCccd,
        icClose,
        icExchange,
        icForgotPassword,
        icHideText,
        icMarket,
        icPhone,
        icSessionExpire,
        icShowText,
        icSmartOtp,
        icSupport,
        icSupportAi,
        icSupportEmail,
        icSupportWeb,
        icSupportZalo,
        icSupportZaloDarkMode,
        loginError,
        mail,
        password,
        phone,
        user
      ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/bg_tet.png
  AssetGenImage get bgTet => const AssetGenImage('assets/images/bg_tet.png');

  /// File path: assets/images/login_background.png
  AssetGenImage get loginBackground =>
      const AssetGenImage('assets/images/login_background.png');

  /// File path: assets/images/splash1.png
  AssetGenImage get splash1 => const AssetGenImage('assets/images/splash1.png');

  /// File path: assets/images/splash2.png
  AssetGenImage get splash2 => const AssetGenImage('assets/images/splash2.png');

  /// File path: assets/images/upload_guide.png
  AssetGenImage get uploadGuide =>
      const AssetGenImage('assets/images/upload_guide.png');

  /// List of all assets
  List<AssetGenImage> get values =>
      [bgTet, loginBackground, splash1, splash2, uploadGuide];
}

class Assets {
  const Assets._();

  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
