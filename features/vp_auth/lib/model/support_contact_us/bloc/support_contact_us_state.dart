part of 'support_contact_us_bloc.dart';

@immutable
abstract class SupportContactUsState extends Equatable {}

class SupportContactUsInfo extends SupportContactUsState {
  final String phone;
  final String email;
  final String address;
  final String website;
  final String zalo;
  final String messenger;
  final String linkedIn;
  final String ai;

  SupportContactUsInfo(
      {this.phone = '',
      this.email = '',
      this.address = '',
      this.zalo = '',
      this.website = '',
      this.messenger = '',
      this.linkedIn = '',
      this.ai = ''});

  @override
  List<Object> get props =>
      [phone, email, address, zalo, messenger, website, linkedIn, ai];
}
