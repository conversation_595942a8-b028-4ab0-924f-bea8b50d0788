import 'dart:ui';

import 'package:app/common/theme/bloc/theme_cubit.dart';
import 'package:app/common/widgets/constains.dart';
import 'package:app/packages/bond/common/widgets/bond_bottom_sheet.dart';
import 'package:app/packages/shared/modules/account/data/model/register/account_laundering_money_item_question_model.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/money_laundering_prevention/list_value.dart';
import 'package:app/packages/stock/common/lang/stock_key_lang.dart';
import 'package:app/packages/stock/common/lang/stock_localized_values.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_ui/widgets/design_system/button/button_widget.dart';
import 'package:flutter/material.dart';

Future showSelectValueBottomSheet({
  required BuildContext context,
  required List<MoneyLaunderingItemQuestionModel> list,
  required MoneyLaunderingItemQuestionModel? selected,
  required String title,
}) async {
  return await showModalBottomSheet(
    barrierColor: ColorUtils.overlayBottomSheet,
    isDismissible: true,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    context: context,
    builder: (context) {
      return BondBottomSheet(
        initialChildSize: MediaQuery.of(context).size.height *0.8 -
            MediaQueryData.fromWindow(window).padding.top -
            kToolbarHeight,
        isFullSize: true,
        child: Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: SelectValueBottomSheet(
            list: list,
            selected: selected,
            title: title,
          ),
        ),
      );
    },
  );
}

class SelectValueBottomSheet extends StatefulWidget {
  const SelectValueBottomSheet(
      {Key? key, required this.list, this.selected, required this.title})
      : super(key: key);
  final List<MoneyLaunderingItemQuestionModel> list;
  final MoneyLaunderingItemQuestionModel? selected;
  final String title;

  @override
  State<SelectValueBottomSheet> createState() => _SelectValueBottomSheetState();
}

class _SelectValueBottomSheetState extends State<SelectValueBottomSheet> {
  MoneyLaunderingItemQuestionModel? _selected;

  @override
  void initState() {
    super.initState();
    _selected = widget.selected;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.title,
            style: TextStyleUtils.text16Weight500,
          ),
          kSpacingHeight8,
          Expanded(
              child: Theme(
            data: ThemeData(
              unselectedWidgetColor:
                  isDark() ? ColorDefine.white : ColorDefine.gray500,
            ),
            child: ListView.builder(
                itemCount: widget.list.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 6),
                    child: Row(
                      children: [
                        SizedBox(
                          width: 24,
                          height: 24,
                          child: Radio(
                            value: widget.list[index],
                            activeColor: ColorDefine.primary,
                            groupValue: _selected,
                            onChanged: (dynamic obj) {
                              setState(() {
                                _selected = obj;
                              });
                            },
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                          ),
                        ),
                        kSpacingWidth8,
                        Expanded(
                          child: Text(
                            widget.list[index].text??'',
                            style: vpTextStyle.body14?,
                          ),
                        ),
                      ],
                    ),
                  );
                }),
          )),
          kSpacingHeight16,
          ButtonWidget(
            action: getStockLang(StockKeyLang.apply),
            onPressed: () {
              Navigator.of(context).pop(_selected);
            },
          ),
        ],
      ),
    );
  }
}
