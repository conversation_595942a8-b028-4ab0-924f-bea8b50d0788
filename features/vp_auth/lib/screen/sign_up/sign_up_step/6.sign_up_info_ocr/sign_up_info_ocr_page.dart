import 'package:app/common/assets/common_key_assets.dart';
import 'package:app/common/extensions/color_exts.dart';
import 'package:app/common/theme/bloc/theme_cubit.dart';
import 'package:app/packages/bond_service/common/constains.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_tracking.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_utils.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/6.sign_up_info_ocr/components/sign_up_info_content_view.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/6.sign_up_info_ocr/sign_up_info_ocr_cubit.dart';
import 'package:core_ui/utils/color_utils.dart';
import 'package:core_ui/utils/size_utils.dart';
import 'package:core_ui/utils/text_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SignUpInfoOcrPage extends StatelessWidget {
  const SignUpInfoOcrPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
        create: (_) => SignUpInfoOcrCubit(), child: _BodySignUpInfoOcr());
  }
}

class _BodySignUpInfoOcr extends StatefulWidget {
  @override
  State<_BodySignUpInfoOcr> createState() => _BodySignUpInfoOcrState();
}

class _BodySignUpInfoOcrState extends State<_BodySignUpInfoOcr> {
  final ScrollController scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: ColorUtils.bgMain,
      child: SingleChildScrollView(
        controller: scrollController,
        child: Padding(
          padding:
              const EdgeInsets.only(top: 24, left: 16, right: 16, bottom: 16),
          child: BlocBuilder<SignUpInfoOcrCubit, SignUpInfoOcrState>(
            builder: (context, state) {
              final obj = state.obj ?? SignUpData().ocrData;
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(getAccountLang(AccountKeyLang.infoCheckGuide),
                      style: TextStyleUtils.text16Weight600
                          .copyWith(color: ColorUtils.black)),
                  kSpacingHeight16,
                  SignUpInfoContentView(
                      title: getAccountLang(AccountKeyLang.fullName),
                      content: obj.fullname),
                  SignUpInfoContentView(
                      title: getAccountLang(AccountKeyLang.birthDay),
                      content: obj.dateofbirth),
                  SignUpInfoContentView(
                      title: getAccountLang(AccountKeyLang.gender),
                      content: SignUpUtils.getGenderString(obj.sex)),
                  SignUpInfoContentView(
                      title: getAccountLang(AccountKeyLang.identifyCard),
                      content: obj.idcode),
                  SignUpInfoContentView(
                      title: getAccountLang(AccountKeyLang.issuedDateCard),
                      content: obj.idDate),
                  SignUpInfoContentView(
                      title: getAccountLang(AccountKeyLang.placeOfIssuedCard),
                      content: obj.idplace),
                  SignUpInfoContentView(
                      title: getAccountLang(AccountKeyLang.permanentAddress),
                      content: obj.address),
                  SignUpInfoContentView(
                      title: getAccountLang(AccountKeyLang.currentAddress),
                      content: obj.address2),
                  const SizedBox(height: 8),
                  _InfoOcrNoteView()
                ],
              );
            },
          ),
        ),
      ),
    );
  }

// /*--------  Build dialog thông báo chuẩn bị chỉnh sửa -------- */
// Future<void> _showMyDialog() async {
//   bloc.trackingEditInfo();
//   await Navigator.push(
//     context,
//     MaterialPageRoute(
//         builder: (context) => const AccountRegisterEditInfo(),
//         fullscreenDialog: true),
//   ).then((data) {
//     if (data is bool && data) {
//       setState(() {});
//       checkWhenResume();
//     }
//     if (data is AccountAction && data == AccountAction.cancel) {
//       Navigator.pop(context);
//     }
//   });
//   // }
// }
}

class _InfoOcrNoteView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 10, right: 10, top: 10),
      decoration: BoxDecoration(
        color: ColorExts.backgroundYellow,
        border: Border.all(
            color: isDark() ? ColorUtils.transparent : ColorUtils.yellow),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Padding(
          padding: const EdgeInsets.all(2),
          child: SvgPicture.asset(CommonKeyAssets.icWarning2),
        ),
        kSpacingWidth4,
        Expanded(
          child: Column(
            children: [
              Text(
                  'Nếu các thông tin trên không trùng khớp với thông tin trên CCCD của bạn, vui lòng tới quầy giao dịch Chứng khoán VPBank để được hỗ trợ hoặc chủ động chỉnh sửa trực tiếp tại đây:',
                  style: TextStyleUtils.text13Weight400
                      .copyWith(color: ColorUtils.gray900)),
              IntrinsicWidth(
                child: Padding(
                  padding: const EdgeInsets.only(right: 26),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        CommonKeyAssets.icEdit,
                        color: ColorUtils.primary,
                        width: 20,
                        height: 20,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 2),
                        child: TextButton(
                            onPressed: () {
                              SignUpRouter()
                                  .onPush(SignUpTypeScreen.editInfoOcr);
                              SignUpTracking().signupProfileInfoEditClick1();
                            },
                            child: Text(
                                getAccountLang(AccountKeyLang.editInfoShort),
                                style: TextStyleUtils.text14Weight500
                                    .copyWith(color: ColorUtils.primary))),
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
        )
      ]),
    );
  }
}
