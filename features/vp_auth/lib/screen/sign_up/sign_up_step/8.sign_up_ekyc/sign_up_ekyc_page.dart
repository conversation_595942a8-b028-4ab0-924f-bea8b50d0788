import 'dart:convert';
import 'dart:io';

import 'package:app/common/utils/app_device_id.dart';
import 'package:app/common/utils/app_routes_utils/app_routes_utils.dart';
import 'package:app/common/utils/app_snackbar_utils.dart';
import 'package:app/global/analytics/app_tracking.dart';
import 'package:app/global/analytics/app_tracking_event.dart';
import 'package:app/global/build_config/stock_build_config.dart';
import 'package:app/global/navigator/app_router.dart';
import 'package:app/global/session/app_session.dart';
import 'package:app/packages/shared/modules/account/common/assets/account_key_assets.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/data/model/model_ekyc_fis/live_ness_model.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/models/onboarding_users_ekyc_action_obj.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/onboarding_path_api.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_tracking.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_utils.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/8.sign_up_ekyc/sign_up_ekyc_cubit.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/fpt_ocr_ekyc/fpt_ekyc/account_camera_ekyc.dart';
import 'package:camera/camera.dart';
import 'package:collection/collection.dart';
import 'package:core/di/injector.dart';
import 'package:core_ui/utils/color_utils.dart';
import 'package:core_ui/utils/size_utils.dart';
import 'package:core_ui/utils/text_utils.dart';
import 'package:ekyc_plugin_flutter/ekyc_plugin_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SignUpEkycPage extends StatelessWidget {
  const SignUpEkycPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
        create: ((_) => SignUpEkycCubit()), child: _BodySignUpEkycPage());
  }
}

class _BodySignUpEkycPage extends StatefulWidget {
  @override
  State<_BodySignUpEkycPage> createState() => _BodySignUpEkycPageState();
}

class _BodySignUpEkycPageState extends State<_BodySignUpEkycPage> {
  late SignUpEkycCubit bloc;

  bool isCheckDoubleTap = false;

  @override
  void initState() {
    super.initState();

    bloc = context.read<SignUpEkycCubit>();
    bloc.setEnable(SignUpData().attachDataObj.getEnableFaceLiveness());
    bloc.updateEnableButtonLiveness();
  }

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: ColorUtils.bgMain,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(getAccountLang(AccountKeyLang.eKycGuide),
                style: vpTextStyle.body14?
                    .copyWith(color: ColorUtils.gray500)),
            const SizedBox(height: 24),
            _ButtonEkyc(onCallBack: () async {
              bloc.gotoCameraSignature(context);
            }),
            const SizedBox(height: 24),
            BlocBuilder<SignUpEkycCubit, SignUpEkycState>(
              buildWhen: (previous, current) =>
                  previous.isValid != current.isValid,
              builder: (context, state) {
                return Text(state.message,
                    style: vpTextStyle.body14?
                        .copyWith(color: ColorUtils.red, height: 1.4));
              },
            )
          ],
        ),
      ),
    );
  }

  Future<String> imageToBase64(String imagePath) async {
    // Đọc dữ liệu ảnh từ đường dẫn local
    File imageFile = File(imagePath);
    if (!imageFile.existsSync()) {
      throw Exception('File not found');
    }

    // Đọc dữ liệu nhị phân từ file ảnh
    List<int> imageBytes = await imageFile.readAsBytes();

    // Chuyển đổi dữ liệu nhị phân sang base64
    String base64Image = base64Encode(imageBytes);

    return base64Image;
  }

  void callAPIGetListAction() async {
    final isSuccess = bloc.isCanEkyc();
    if (isSuccess || isCheckDoubleTap) {
      return;
    }
    isCheckDoubleTap = true;
    bloc.callAPIGetListAction().then((value) {
      isCheckDoubleTap = false;
      if (value != null) {
        gotoCameraEkyc(value);
        SignUpTracking().signupFaceRegClick();
      }
    });
  }

  Future gotoCameraEkyc(
      OnboardingUsersAccEkycActionResponseObj listAction) async {
    final context = navigation.navigatorKey.currentContext;
    if (context == null) {
      return;
    }
    final isSuccess = await SignUpPermissionUtils.checkPermissionCamera();
    if (isSuccess) {
      final cameras = await availableCameras();
      final camera = cameras.firstWhereOrNull(
          (element) => element.lensDirection == CameraLensDirection.front);
      if (cameras.isNotEmpty && camera != null) {
        RouterUtils.pushFullScreen(
            AccountCameraEkyc(camera: camera, listAction: listAction),
            onCallBack: (msgEkyc) {
          bloc.checkEmitEkyc(msgEkyc);
        });
      }
    }
  }
}

class _ButtonEkyc extends StatelessWidget {
  final Function onCallBack;

  const _ButtonEkyc({Key? key, required this.onCallBack}) : super(key: key);

  @override
  Widget build(Object context) {
    return SizedBox(
      height: 40,
      child: BlocBuilder<SignUpEkycCubit, SignUpEkycState>(
        builder: (context, state) {
          final isSuccess = state.isValid ?? false;
          return ElevatedButton(
            style: ButtonStyle(
              elevation: MaterialStateProperty.all<double>(isSuccess ? 0 : 1),
              backgroundColor: MaterialStateProperty.all<Color>(
                  isSuccess ? ColorUtils.highlightBg : ColorUtils.primary),
              shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            child: IntrinsicWidth(
              child: Row(children: [
                isSuccess
                    ? SvgPicture.asset(AccountKeyAssets.icVerify,
                        color: ColorUtils.primary)
                    : SvgPicture.asset(AccountKeyAssets.icCamera,
                        color: ColorDefine.white),
                const SizedBox(
                  width: 8,
                ),
                Text(
                    getAccountLang(isSuccess
                        ? AccountKeyLang.verifyFaceSuccess
                        : AccountKeyLang.startEkyc),
                    style: TextStyleUtils.text14Weight600.copyWith(
                        color: isSuccess
                            ? ColorUtils.primary
                            : ColorDefine.white)),
              ]),
            ),
            onPressed: () {
              onCallBack();
              AppTracking.instance.logEvent(
                name: AppTrackingEvent.signupFaceRegClick,
                type: TrackingType.appsflyer,
              );
            },
          );
        },
      ),
    );
  }
}
