import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vp_auth/gen/assets.gen.dart';
import 'package:vp_auth/generated/l10n.dart';
import 'package:vp_auth/model/support_contact_us/bloc/support_contact_us_bloc.dart';
import 'package:vp_common/vp_common.dart' hide Assets;
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_common/utils/app_launching_utils.dart';

class DialogSelectSupport extends StatelessWidget {
 // final SupportContactUsInfo supportContact;
  final VoidCallback? onAiPress;
  final bool? consultant;

  const DialogSelectSupport(
      {Key? key,  this.consultant, this.onAiPress})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final  SupportContactUsInfo  supportContact = SupportContactUsInfo(
      phone: '1900 636679',
      email: '<EMAIL>',
      zalo: AppConstants.zalo,
      website: 'www.vpbanks.com.vn');
    final widgets = [];
    if (supportContact.phone.isNotEmpty) {
      widgets.add(ItemDialogSupport(
        icon: Assets.icons.phone,
        colorIcon: themeData.primary,
        title: supportContact.phone,
        onTap: () => callNow(supportContact.phone),
      ));
    }
    if (supportContact.email.isNotEmpty) {
      widgets.add(ItemDialogSupport(
        icon: Assets.icons.icSupportEmail,
        colorIcon: themeData.primary,
        sizeIcon: 16,
        title: supportContact.email,
        onTap: () => sendMail(supportContact.email),
      ));
    }
    if (supportContact.zalo.isNotEmpty) {
      widgets.add(ItemDialogSupport(
        icon: context.isDark
            ? Assets.icons.icSupportZaloDarkMode 
            : Assets.icons.icSupportZalo,
        sizeIcon: 24,
        title: (consultant ?? false)
            ? S.current.account_support_zalo
            : S.current.account_support_zalo2,
        onTap: () => tryOpenApp(supportContact.zalo),
      ));
    }
    if (supportContact.website.isNotEmpty) {
      widgets.add(ItemDialogSupport(
        icon: Assets.icons.icSupportWeb,
        colorIcon: themeData.primary,
        title: '${supportContact.website}',
        onTap: () => openBrowser('https://' + supportContact.website),
      ));
    }

    if (supportContact.ai.isNotEmpty) {
      widgets.add(ItemDialogSupport(
        icon:  Assets.icons.icSupportAi,
        title: supportContact.ai,
        onTap: () {
          onAiPress?.call();
        },
      ));
    }

    assert(
        widgets.isNotEmpty, 'There must be at least one support in the list');
    return SafeArea(
      child: Container(
        padding: const EdgeInsets.all(8.0),
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(50.0),
            topRight: Radius.circular(50.0),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: themeData.bgPopup,
                borderRadius: const BorderRadius.all(
                  Radius.circular(12.0),
                ),
              ),
              child: ListView.separated(
                itemBuilder: (_, index) => widgets[index],
                separatorBuilder: (_, __) => const DividerWidget(),
                itemCount: widgets.length,
                shrinkWrap: true,
              ),
            ),
            const SizedBox(
              height: 8,
            ),
            ButtonBottomSheet(
                color: themeData.red,
                text: VPCommonLocalize.current.cancel,
                onTap: () => Navigator.pop(context)),
          ],
        ),
      ),
    );
  }
}

class ItemDialogSupport extends StatelessWidget {
  final String icon;
  final String title;
  final VoidCallback onTap;
  final Color? colorIcon;
  final double? sizeIcon;

  const ItemDialogSupport(
      {Key? key,
      required this.icon,
      required this.title,
      required this.onTap,
      this.sizeIcon,
      this.colorIcon})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              package: 'vp_auth',
              icon,
              width: sizeIcon ?? 18,
              height: sizeIcon ?? 18,
              colorFilter: colorIcon != null
                  ? ColorFilter.mode(colorIcon ?? themeData.primary,
                      BlendMode.srcIn) // Nếu có màu thì thay đổi
                  : null, // Nếu null thì giữ nguyên màu gốc
            ),
            const SizedBox(
              width: 18,
            ),
            Text(
              title,
              style: vpTextStyle.subtitle16
                  ?.copyWith(color: Theme.of(context).iconTheme.color),
            ),
          ],
        ),
      ),
    );
  }
}
