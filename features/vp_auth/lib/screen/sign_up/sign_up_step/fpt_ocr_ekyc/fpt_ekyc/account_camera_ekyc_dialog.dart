import 'dart:async';

import 'package:app/common/widgets/app_dot_view.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/view/components/sign_up_button.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:core_ui/utils/color_utils.dart';
import 'package:core_ui/utils/size_utils.dart';
import 'package:core_ui/utils/text_utils.dart';
import 'package:flutter/material.dart';

import '../../../../../../common/assets/account_key_assets.dart';
import '../../../../../../common/lang/account_key_lang.dart';
import '../../../../../../common/lang/account_localized_values.dart';

class AccountCameraEkycDialog extends StatefulWidget {
  const AccountCameraEkycDialog({Key? key, required this.callback})
      : super(key: key);

  final Function callback;

  @override
  State<AccountCameraEkycDialog> createState() =>
      _AccountCameraEkycDialogState();
}

class _AccountCameraEkycDialogState extends State<AccountCameraEkycDialog> {
  int currentPage = 0;
  final controllerUI = StreamController.broadcast();
  final PageController _controller = PageController();
  late Timer periodicTimer;

  @override
  void initState() {
    super.initState();
    periodicTimer = Timer.periodic(
      const Duration(seconds: 5),
      (_) {
        currentPage == 0 ? currentPage++ : currentPage--;
        nextPage();
      },
    );
  }

  @override
  void dispose() {
    controllerUI.close();
    _controller.dispose();
    periodicTimer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      color: Colors.black.withOpacity(0.7),
      child: Center(child: buildDialogEkyc(context)),
    );
  }

  /// DIALOG EKYC
  Widget buildDialogEkyc(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.8,
      decoration: BoxDecoration(
          color: ColorDefine.white,
          borderRadius: BorderRadius.circular(SizeUtils.kRadius16)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: IntrinsicHeight(
          child: Column(children: [
            SizedBox(
              height: 230,
              child: PageView.builder(
                physics: const NeverScrollableScrollPhysics(),
                onPageChanged: (index) {
                  controllerUI.sink.add(true);
                },
                controller: _controller,
                itemCount: 2,
                itemBuilder: (context, index) {
                  return buildItemPage(index);
                },
              ),
            ),
            StreamBuilder(
                stream: controllerUI.stream,
                builder: (context, snapshot) {
                  return AppDotView(length: 2, currentPage: currentPage);
                }),
            StreamBuilder(
                stream: controllerUI.stream,
                builder: (context, snapshot) {
                  return SignUpButton(
                      color: ColorDefine.primary,
                      text: getAccountLang(currentPage == 0
                          ? AccountKeyLang.next
                          : AccountKeyLang.startEkyc),
                      press: () {
                        onStartEkyc();
                      });
                })
          ]),
        ),
      ),
    );
  }

  /// ITEM PAGE VIEW
  Column buildItemPage(int index) {
    final isFirstPage = index == 0;
    const size = 80;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: Image.asset(
            isFirstPage
                ? AccountKeyAssets.ekycSuccess
                : AccountKeyAssets.ekycError,
          ),
        ),
        const SizedBox(height: 8),
        Text(
            getAccountLang(isFirstPage
                ? AccountKeyLang.eKycTitle
                : AccountKeyLang.notRotate90),
            style: TextStyleUtils.text20Weight700
                .copyWith(color: ColorDefine.gray900)),
        const SizedBox(height: 12),
        Expanded(
          child: AutoSizeText(
            getAccountLang(isFirstPage
                ? AccountKeyLang.guideEkyc1
                : AccountKeyLang.guideEkyc2),
            style: TextStyleUtils.text16Weight400
                .copyWith(color: ColorDefine.gray700),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  /// Start Ekyc
  void onStartEkyc() {
    if (currentPage == 1) {
      widget.callback();
      return;
    }
    currentPage++;
    nextPage();
  }

  /// Next Page
  void nextPage() {
    _controller.animateToPage(currentPage,
        duration: const Duration(milliseconds: 300), curve: Curves.linear);
  }
}
