import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_price_board/widget/sort/normal_sort_view.dart';
import 'package:vp_price_board/widget/stock_list_item.dart';
import 'package:vp_price_board/widget/stock_list_item_builder_view.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

import 'stock_index_bloc.dart';

class StockIndexTabView extends StatefulWidget {
  const StockIndexTabView({required this.indexCode, super.key});

  final IndexCode indexCode;

  @override
  State<StockIndexTabView> createState() => _StockIndexTabViewState();
}

class _StockIndexTabViewState extends State<StockIndexTabView>
    with AutomaticKeepAliveClientMixin {
  ScrollController controller = ScrollController();

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create:
              (_) => StockIndexBloc(indexCode: widget.indexCode)..loadData(),
        ),
      ],

      child: BlocBuilder<StockIndexBloc, StockIndexState>(
        builder: (context, state) {
          return StockListItemBuilderView(
            apiStatus: state.apiStatus,
            data: state.items,
            onRetry: () => onRefresh(context),
            builder: (context, data) {
              return NormalSortView(
                onChanged: onSort,
                sortType: state.sortType,
                status: state.sortStatus,
                builder: (context, columns) {
                  return StockListView(
                    items: data,
                    columns: columns,
                    controller: controller,
                    onRefresh: () => onRefresh(context),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }

  Future onRefresh(BuildContext context) =>
      context.read<StockIndexBloc>().onRefresh();

  void onSort(BuildContext context, SortMode mode) {
    context.read<StockIndexBloc>().sortBy(mode);

    controller.jumpTo(0);
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;
}
