
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vp_auth/generated/l10n.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/cubit/sign_up_bloc.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/cubit/sign_up_state.dart';

class SignUpHeaderView extends StatelessWidget {
  const SignUpHeaderView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8, right: 8, left: 16),
      child: Row(
        children: [
          Expanded(
            child: BlocBuilder<SignUpBloc, SignUpState>(buildWhen: (a, b) {
              return b is SignUpStateTitle;
            }, builder: (context, state) {
              final title = (state is SignUpStateTitle) ? state.title : '';
              final value = title.isEmpty
                  ? S.current.account_declare_information
                  : title;
              String subTitle =
                  (state is SignUpStateTitle) ? (state.subTitle ?? '') : '';
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                      subTitle.isNotEmpty
                          ? subTitle
                          : S.current.account_register,
                      style: TextStyleUtils.text12Weight400.copyWith(
                        color: ColorUtils.gray500,
                      )),
                  Text(value,
                      style: TextStyleUtils.text20Weight700
                          .copyWith(color: ColorUtils.primary)),
                ],
              );
            }),
          ),
          IconButton(
              onPressed: () async {
                showExitSignUp();
              },
              icon:
                  Icon(Icons.close, color: ColorUtils.colorIcon, size: Icon26))
        ],
      ),
    );
  }
}

Future showExitSignUp() async {
  bool result = false;
  final context = navigation.navigatorKey.currentContext;
  if (context == null) {
    return result;
  }
  await appShowDialog(
      allowDismiss: true,
      iconWidget: SvgPicture.asset(CommonKeyAssets.icCancel),
      titleWidget: TitleDialog(
        title: S.current.account_cancel_register_title,
        titleStyle:
            TextStyleUtils.text20Weight700.copyWith(color: ColorUtils.gray700),
      ),
      contentWidget: ContentDialog(
          contentStyle: TextStyleUtils.text14Weight500
              .copyWith(color: ColorUtils.gray500),
          content: S.current.account_cancel_register_content_2),
      actionWidget: ActionDialog(
          textButtonRight: getAccountLang(AccountKeyLang.cancelRegister),
          colorButtonRight: ColorUtils.red,
          textButtonLeft: getAccountLang(AccountKeyLang.back),
          colorBorderButtonLeft: ColorUtils.borderBg,
          textStyleLeft:
              TextStyleUtils.text14Weight600.copyWith(color: ColorUtils.black),
          onPressedLeft: () {
            Navigator.pop(context);
          },
          onPressedRight: () {
            SignUpTracking().signUpClose();
            Navigator.pop(context);
            result = true;
          }));
  if (result) {
    AppKeyboardUtils.dismissKeyboard();
    navigation.goBack();
  }
}
