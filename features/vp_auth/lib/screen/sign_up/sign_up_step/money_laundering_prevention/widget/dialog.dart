
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:url_launcher/url_launcher.dart';

Future<void> showPopupPhoneNumber(BuildContext context) async {
  showNotifyDialog(
    allowDismiss: true,
    context: context,
    image: CommonKeyAssets.icWarning,
    onPressedRight: () => Navigator.pop(context),
    title: getBondLang(BondKeyLang.notification),
    contentWidget: Text(
      getAccountLang(AccountKeyLang.phoneNumberUsed),
      textAlign: TextAlign.center,
      style: vpTextStyle.body14.copyColor(vpColor.gray700),
    ),
    textButtonRight: getAccountLang(AccountKeyLang.understood),
  );
}

Future<void> showPopupOpenAnAccount(BuildContext context) async {
  showNotifyDialog(
    allowDismiss: true,
    context: context,
    image: CommonKeyAssets.icWarning,
    onPressedRight: () => Navigator.pop(context),
    title: getAccountLang(AccountKeyLang.openAnAccount),
    contentWidget: RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        children: [
          TextSpan(
            text: getAccountLang(AccountKeyLang.noteOpenAnAccount1),
          ),
          TextSpan(
              text: AppConstants.support,
              style: vpTextStyle.body14?
                  .copyWith(color: ColorUtils.primary),
              recognizer: TapGestureRecognizer()
                ..onTap = () async {
                  final Uri launchUri = Uri(
                    scheme: 'tel',
                    path: AppConstants.support,
                  );
                  await launchUrl(launchUri);
                }),
          TextSpan(
            text: getAccountLang(AccountKeyLang.noteOpenAnAccount2),
          ),
        ],
        style: vpTextStyle.body14?.copyWith(
          color: ColorUtils.gray700,
          height: 1.5,
        ),
      ),
    ),
    textButtonRight: getAccountLang(AccountKeyLang.understood),
  );
}
