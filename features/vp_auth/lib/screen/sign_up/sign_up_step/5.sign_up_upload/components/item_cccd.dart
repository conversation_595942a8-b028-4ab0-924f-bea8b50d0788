import 'dart:io';

import 'package:app/common/assets/common_key_assets.dart';
import 'package:app/packages/shared/modules/account/common/assets/account_key_assets.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_attach_data.dart';
import 'package:core_ui/core_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../../../../../../../common/widgets/constains.dart';

class ItemCCCD extends StatelessWidget {
  final String title;
  final String pathImage;
  final bool? isFocus;
  final Function(UploadImageType)? onCallBack;
  final UploadImageType typeUpload;

  const ItemCCCD(
      {Key? key,
      required this.title,
      required this.typeUpload,
      this.pathImage = '',
      this.isFocus = false,
      this.onCallBack})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final _isFocus = isFocus ?? false;
    final size = MediaQuery.of(context).size;
    return Column(
      children: [
        InkWell(
          onTap: () => onCallBack!(typeUpload), // Sửa lại cách gọi onTap
          child: Container(
            padding: EdgeInsets.symmetric(vertical: _isFocus ? 5.5 : 8),
            decoration: BoxDecoration(
                border: Border.all(color: ColorUtils.primary, width: 0.6),
                borderRadius: BorderRadius.circular(8)),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                kSpacingWidth12,
                Expanded(
                  child: pathImage != ''
                      ? Image.file(File(pathImage))
                      : Image.asset(AccountKeyAssets.uploadGuide),
                ),
                SvgPicture.asset(
                  CommonKeyAssets.icCheck2,
                  width: 16,
                  color: ColorUtils.primary,
                ),
              ],
            ),
          ),
        ),
        SizedBox(width: 12), // Sử dụng SizedBox để tạo khoảng cách
        Text(
          title,
          style: TextStyleUtils.text13Weight500
              .copyWith(color: ColorUtils.gray900),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
