import 'package:rxdart/rxdart.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:vp_common/utils/stream_utils.dart';

class SignUpFlow extends BaseStream {
  static final SignUpFlow _singleton = SignUpFlow._internal();

  factory SignUpFlow() => _singleton;

  SignUpFlow._internal();

  init() {
    _enableController = BehaviorSubject();
    _titleController = BehaviorSubject();
    _processController = BehaviorSubject();
    _onResumeController = BehaviorSubject();
    _onNextController = PublishSubject();
    _loadingController = PublishSubject();
  }

  late PublishSubject<SignUpTypeScreen> _onNextController;

  Stream<SignUpTypeScreen> get onNextStream => _onNextController.stream;

  late BehaviorSubject<ArgumentEnableBtnNext> _enableController;

  Stream<ArgumentEnableBtnNext> get enableStream => _enableController.stream;

  late BehaviorSubject<ArgumentTitleStream> _titleController;

  Stream<ArgumentTitleStream> get titleStream => _titleController.stream;

  late BehaviorSubject<SignUpTypeScreen> _onResumeController;

  Stream<SignUpTypeScreen> get onResumeStream => _onResumeController.stream;

  late BehaviorSubject<int> _processController;

  Stream<int> get processStream => _processController.stream;

  PublishSubject<bool>? _loadingController;

  Stream<bool>? get loadingStream => _loadingController?.stream;

  void emitLoading(bool isLoading) {
    emitStreamData(_loadingController, isLoading);
  }

  void emitEnable(bool value, {String? title}) {
    emitStreamData(
        _enableController, ArgumentEnableBtnNext(value, title ?? ''));
  }

  void emitTitle(String value, {String? subTitle}) {
    final data = ArgumentTitleStream(title: value, subTitle: subTitle);

    emitStreamData(_titleController, data);
  }

  void emitProcess(int value) {
    emitStreamData(_processController, value);
  }

  void emitOnNext(SignUpTypeScreen type) {
    emitStreamData(_onNextController, type);
  }

  @override
  void onResume() {
    emitStreamData(_onResumeController, SignUpRouter().currentScreen);
  }

  @override
  void dispose() {
    _enableController.close();
    _titleController.close();
    _onResumeController.close();
    _processController.close();
    _onNextController.close();
    _loadingController?.close();
  }
}

class ArgumentEnableBtnNext {
  final bool enable;
  final String title;

  ArgumentEnableBtnNext(this.enable, this.title);
}

class ArgumentTitleStream {
  final String title;
  final String? subTitle;

  ArgumentTitleStream({required this.title, this.subTitle});
}
