import 'package:app/global/analytics/app_tracking.dart';
import 'package:app/global/analytics/app_tracking_event.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_router.dart';

class SignUpTracking {
  static final SignUpTracking _singleton = SignUpTracking._internal();
  factory SignUpTracking() => _singleton;
  SignUpTracking._internal();

  void signupPhoneNumSuccess() {
    tracking(name: AppTrackingEvent.signupPhoneNumSuccess);
  }

  /// OTP
  void signupOtpSuccess() {
    tracking(name: AppTrackingEvent.signupOtpSuccess);
  }

  void signupOtpFail(dynamic value) {
    tracking(name: AppTrackingEvent.signupOtpFail, param: _getParamErr(value));
  }

  /// Email
  void signupEmailSuccess() {
    tracking(name: AppTrackingEvent.signupEmailSuccess);
  }

  void signupEmailFail(dynamic value) {
    tracking(
        name: AppTrackingEvent.signupEmailFail, param: _getParamErr(value));
  }

  /// Bank
  void signupBankInfoSuccess() {
    tracking(name: AppTrackingEvent.signupBankInfoSuccess);
  }

  void signupBankInfoFail(dynamic value) {
    tracking(
        name: AppTrackingEvent.signupBankInfoFail, param: _getParamErr(value));
  }

  void signupRegisterBankAccount() {
    tracking(name: AppTrackingEvent.signupRegisterBankAccount);
  }

  /// Support
  void signupBrokerSelectOption(String value) {
    tracking(
        name: AppTrackingEvent.signupBrokerSelectOption,
        param: {'selection': value});
  }

  void signupBrokerRegSuccess() {
    tracking(name: AppTrackingEvent.signupBrokerRegSuccess);
  }

  void signupBrokerRegFail(dynamic value) {
    tracking(
        name: AppTrackingEvent.signupBrokerRegFail, param: _getParamErr(value));
  }

  /// UPLOAD
  void signupProfilePhotoSuccess() {
    tracking(name: AppTrackingEvent.signupProfilePhotoSuccess);
  }

  void signupIdcardFrontSuccess() {
    tracking(name: AppTrackingEvent.signupIDcardFrontSuccess);
  }

  void signupIdcardFrontFail() {
    tracking(name: AppTrackingEvent.signupIdcardFrontFail);
  }

  void signupIdcardBackSuccess() {
    tracking(name: AppTrackingEvent.signupIdcardBackSuccess);
  }

  void signupIdcardBackFail() {
    tracking(name: AppTrackingEvent.signupIdcardBackFail);
  }

  void signupSignatureSuccess() {
    tracking(name: AppTrackingEvent.signupSignatureSuccess);
  }

  void signupSignatureFail() {
    tracking(name: AppTrackingEvent.signupSignatureFail);
  }

  /// ACC - NUM
  void signupAccountNumSuccess() {
    tracking(name: AppTrackingEvent.signupAccountNumSuccess);
  }

  void signupAccountNumFail(dynamic value) {
    tracking(
        name: AppTrackingEvent.signupAccountNumFail,
        param: _getParamErr(value));
  }

  /// INFO - OCR
  void signupProfileConfirmSuccess() {
    tracking(name: AppTrackingEvent.signupProfileConfirmSuccess);
  }

  void signupProfileInfoEditClick1() {
    tracking(name: AppTrackingEvent.signupProfileInfoEditClick1);
  }

  void signupProfileInfoEditSuccess() {
    tracking(name: AppTrackingEvent.signupProfileInfoEditSuccess);
  }

  /// EKYC
  void signupFaceRegClick() {
    tracking(name: AppTrackingEvent.signupFaceRegClick);
  }

  void signupFaceRegSuccess() {
    tracking(name: AppTrackingEvent.signupFaceRegSuccess);
  }

  void signupFaceRegFail() {
    tracking(name: AppTrackingEvent.signupFaceRegFail);
  }

  /// CONTRACT
  void signupContractSuccess() {
    tracking(name: AppTrackingEvent.signupContractSuccess);
  }

  Map<String, dynamic> _getParamErr(dynamic value) {
    return {'Error': value.toString()};
  }

  /// CLOSE
  void signUpClose() {
    final type = SignUpRouter().currentScreen;
    final mapEventClose = {
      SignUpTypeScreen.phone.name: AppTrackingEvent.signupPhoneNumClose,
      SignUpTypeScreen.otp.name: AppTrackingEvent.signupOtpClose,
      SignUpTypeScreen.email.name: AppTrackingEvent.signupEmailClose,
      SignUpTypeScreen.bank.name: AppTrackingEvent.signupBankInfoClose,
      SignUpTypeScreen.supporter.name:
          AppTrackingEvent.signupBrokerSelectOptionClose,
      SignUpTypeScreen.upload.name: AppTrackingEvent.signupProfilePhotoClose,
      SignUpTypeScreen.infoOcr: AppTrackingEvent.signupProfileConfirmClose,
      SignUpTypeScreen.contract: AppTrackingEvent.signupContractClose
    };
    tracking(name: mapEventClose[type.name] ?? '');
  }

  /// BACK
  void signupBack() {
    final type = SignUpRouter().currentScreen;
    final mapEventClose = {
      SignUpTypeScreen.phone.name: AppTrackingEvent.signupPhoneNumBack,
      SignUpTypeScreen.otp.name: AppTrackingEvent.signupOtpBack,
      SignUpTypeScreen.email.name: AppTrackingEvent.signupEmailBack,
      SignUpTypeScreen.bank.name: AppTrackingEvent.signupBankInfoBack,
      SignUpTypeScreen.supporter.name:
          AppTrackingEvent.signupBrokerSelectOptionBack,
      SignUpTypeScreen.upload.name: AppTrackingEvent.signupProfilePhotoBack,
      SignUpTypeScreen.infoOcr: AppTrackingEvent.signupProfileConfirmBack,
      SignUpTypeScreen.contract: AppTrackingEvent.signupContractBack
    };
    tracking(name: mapEventClose[type.name] ?? '');
  }

  /// ----------- TRACKING -------- ///
  void tracking({required String name, Map<String, dynamic>? param}) {
    AppTracking.instance.logEvent(
      parameters: param,
      name: name,
      type: TrackingType.appsflyer,
    );
  }
}
