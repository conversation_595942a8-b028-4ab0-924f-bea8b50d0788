import 'package:app/common/widgets/constains.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/4.1sign_up_nfc/sign_up_scan_nfc_bloc.dart';
import 'package:app/packages/shared/modules/settings/assets/settings_key_assets.dart';
import 'package:app/packages/shared/modules/settings/common/enum/navigate_nfc_type.dart';
import 'package:app/packages/stock/modules/place_order/presentation/feature/place_order/bloc/place_order_bloc_map_import.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SignUpScanNfcRequest {
  final String id;
  final String dob;
  final String doe;
  final String doi;
  final String idDate;
  final String? idTypeNew;
  final NavigateNFCType? nfcType;

  const SignUpScanNfcRequest(
      {required this.id,
      required this.dob,
      required this.doe,
      required this.doi,
      required this.idDate,
      this.idTypeNew,
      this.nfcType});
}

class SignUpScanNfcPage extends StatelessWidget {
  const SignUpScanNfcPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => SignUpScanNFCBloc(),
      child: _SignUpScanNfcPage(),
    );
  }
}

class _SignUpScanNfcPage extends StatefulWidget {
  @override
  State<_SignUpScanNfcPage> createState() => _SignUpScanNfcPageState();
}

class _SignUpScanNfcPageState extends State<_SignUpScanNfcPage> {
  late SignUpScanNFCBloc bloc;

  @override
  void initState() {
    super.initState();
    bloc = context.read<SignUpScanNFCBloc>();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: SingleChildScrollView(
            child: Column(
              children: [
                kSpacingHeight16,
                Text(
                  'Đặt khu vực chip của thẻ CCCD lên mặt sau, phía trên điện thoại để bắt đầu quét. Di chuyển thẻ quanh khu vực camera cho đến khi nhận tín hiệu. Sau đó, giữ thẻ đúng vị trí đến khi thông báo thành công',
                  style: TextStyleUtils.text16Weight400
                      .copyWith(color: ColorUtils.gray900),
                ),
                kSpacingHeight16,
                Image.asset(
                  SettingsKeyAssets.imageScanNfc,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
