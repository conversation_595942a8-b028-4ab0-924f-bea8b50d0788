
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_auth/core/constant/sign_up_type.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/cubit/sign_up_bloc.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/sign_up_flow.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/sign_up_router.dart' hide SignUpTypeScreen;
import 'package:vp_auth/screen/sign_up/sign_up_page/view/components/sign_up_bottom_view.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/view/components/sign_up_header_view.dart';

class SignUpPage extends StatefulWidget {
  const SignUpPage({this.navigateFromDeeplink = false, Key? key})
      : super(key: key);

  final bool navigateFromDeeplink;

  @override
  State<SignUpPage> createState() => _SignUpPageState();
}

class _SignUpPageState extends State<SignUpPage> {
  @override
  void initState() {
    super.initState();

    final clearRrefcustodycd = !widget.navigateFromDeeplink;

    SignUpData().clear(clearRrefcustodycd: clearRrefcustodycd);
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      child: BlocProvider(
        create: (_) => SignUpBloc(),
        child: _BodySignUpPage(),
      ),
      onWillPop: () async {
        showExitSignUp();
        return false;
      },
    );
  }
}

class _BodySignUpPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      loadingStream: SignUpFlow().loadingStream,
      child: SafeArea(
        child: Column(
          children: [
            const SignUpHeaderView(),
            Expanded(
              child: Navigator(
                initialRoute: SignUpTypeScreen.phone.name,
                onGenerateRoute: SignUpRouter().onGenerateRoute,
                key: SignUpRouter().navigatorKey,
              ),
            ),
            const SignUpBottomView()
          ],
        ),
      ),
    );
  }
}
