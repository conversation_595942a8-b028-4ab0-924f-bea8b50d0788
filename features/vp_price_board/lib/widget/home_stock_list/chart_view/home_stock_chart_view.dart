import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_price_board/widget/home_stock_list/chart_view/home_stock_chart_cubit.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class HomeStockChartView extends StatelessWidget {
  const HomeStockChartView({
    required this.stock,
    required this.chartData,
    super.key,
  });

  final List<ChartData> chartData;

  final StockInfoModel stock;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<StockDetailChartCubit>(
      create: (_) => StockDetailChartCubit(chartData, stock: stock)..init(),
      child: BlocBuilder<StockDetailChartCubit, HomeStockChartState>(
        builder: (_, state) {
          return IntradayChartView(
            minY: -20,
            maxY: 120,
            chartData: state.chartData,
            primaryYAxisVisible: false,
            referencePrice: stock.refPrice!.toDouble(),
            floorPrice: stock.floor!.toDouble(),
            ceilingPrice: stock.floor!.toDouble(),
          );
        },
      ),
    );
  }
}
