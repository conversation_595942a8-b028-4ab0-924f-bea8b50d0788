import 'package:flutter/cupertino.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_price_board/model/paging_and_sorting_mixin.dart';
import 'package:vp_price_board/model/sorting/paged_sorted_state.dart';
import 'package:vp_stock_common/core/repository/watchlist_repository.dart';
import 'package:vp_stock_common/model/enum/chart_period.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

part 'home_suggestion_category_state.dart';

class HomeSuggestionStockCubit extends AppCubit<HomeSuggestionStockState>
    with StockInfoSocketMixin {
  HomeSuggestionStockCubit({required List<WatchlistModel>? watchlists})
    : super(
        HomeSuggestionStockState(
          watchlists: watchlists,
          apiStatus: ApiStatus.initial(),
        ),
      );

  final StockCommonRepository stockCommonRepository =
      GetIt.instance.get<StockCommonRepository>();

  final WatchlistRepository watchlistRepository =
      GetIt.instance.get<WatchlistRepository>();

  final ChartRepository chartRepository = GetIt.instance.get<ChartRepository>();

  Future loadData() async {
    try {
      emit(state.copyWith(apiStatus: ApiStatus.loading()));

      final watchlistDefault = await watchlistRepository.getWatchListDefault();

      final watchlistSelected =
          state.watchlists?.findFirstOrNull(
            (e) => e.id == watchlistDefault?.id,
          ) ??
          state.watchlists.firstOrNull ??
          await stockCommonRepository.getSuggestedLists();

      await getStockDetail(watchlistSelected);
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      emit(state.copyWith(apiStatus: ApiStatus.error(e)));
    }
  }

  Future getStockDetail(WatchlistModel? watchlist) async {
    try {
      unsubscribeStockInfo();

      emit(
        state.copyWith(
          apiStatus: ApiStatus.loading(),
          watchlistSelected: watchlist,
        ),
      );

      final symbols = state.watchlistSelected?.symbols;

      if (symbols.isNullOrEmpty) {
        return emit(state.resetData(apiStatus: ApiStatus.done()));
      }

      final data = await Future.wait([
        stockCommonRepository.getStockInfoV2(symbols: symbols),
        getChartData(symbols),
      ]);

      subscribeStockInfo(symbols?.toSet());

      emit(
        state.copyWith(
          apiStatus: ApiStatus.done(),
          chartData: data.lastOrNull as Map<String, List<ChartData<dynamic>>>?,
          paging: data.firstOrNull as BasePagingResponse<StockInfoModel>?,
        ),
      );
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  Future<Map<String, List<ChartData>>?> getChartData(
    List<String>? symbols,
  ) async {
    try {
      final chartData = await chartRepository.getChartData(
        symbols: symbols!,
        period: ChartPeriod.fiveMinutes,
      );

      return chartData;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }

    return null;
  }

  void onWatchlistChanged(WatchlistModel watchlist) {
    getStockDetail(watchlist);
  }

  Future onRefresh() => loadData();

  @override
  Future<void> close() {
    unsubscribeStockInfo();
    return super.close();
  }
}
