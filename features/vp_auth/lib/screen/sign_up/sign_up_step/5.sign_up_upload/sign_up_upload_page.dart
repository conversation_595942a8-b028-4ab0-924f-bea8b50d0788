import 'dart:convert';
import 'dart:io';

import 'package:app/common/theme/bloc/theme_cubit.dart';
import 'package:app/common/utils/app_device_id.dart';
import 'package:app/common/utils/app_routes_utils/app_routes_utils.dart';
import 'package:app/common/utils/app_snackbar_utils.dart';
import 'package:app/common/utils/permission_utils.dart';
import 'package:app/common/widgets/app_camera/app_camera_page.dart';
import 'package:app/common/widgets/constains.dart';
import 'package:app/global/build_config/stock_build_config.dart';
import 'package:app/global/navigator/app_router.dart';
import 'package:app/global/session/app_session.dart';
import 'package:app/packages/shared/modules/account/common/assets/account_key_assets.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/data/model/model_ekyc_fis/ocr_ekyc.dart';
import 'package:app/packages/shared/modules/account/data/model/model_ekyc_fis/ocr_ekyc_response.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/onboarding_path_api.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_attach_data.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_utils.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/4.1sign_up_nfc/widget/popup_warning.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/fpt_ocr_ekyc/fpt_ocr/account_camera_ocr.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/fpt_ocr_ekyc/fpt_ocr/account_camera_ocr_bloc.dart';
import 'package:camera/camera.dart';
import 'package:collection/collection.dart';
import 'package:core/di/injector.dart';
import 'package:core_ui/utils/color_utils.dart';
import 'package:core_ui/utils/size_utils.dart';
import 'package:core_ui/utils/text_utils.dart';
import 'package:core_ui/widgets/design_system/button/button_widget.dart';
import 'package:ekyc_plugin_flutter/ekyc_plugin_flutter.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'components/item_cccd.dart';
import 'components/item_description_view.dart';
import 'sign_up_upload_cubit.dart';

class SignUpUploadPage extends StatelessWidget {
  const SignUpUploadPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => SignUpUploadCubit(),
      child: _BodySignUpUpload(),
    );
  }
}

class _BodySignUpUpload extends StatefulWidget {
  @override
  State<_BodySignUpUpload> createState() => _BodySignUpUploadState();
}

class _BodySignUpUploadState extends State<_BodySignUpUpload> {
  late SignUpUploadCubit bloc;

  bool checkDoubleTap = false;

  @override
  void initState() {
    super.initState();

    bloc = context.read<SignUpUploadCubit>();
  }

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: ColorUtils.bgMain,
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Text(getAccountLang(AccountKeyLang.takePhotoSignature),
              //     style: TextStyleUtils.text16Weight400
              //         .copyWith(color: ColorUtils.gray900)),
              kSpacingHeight16,
              BlocBuilder<SignUpUploadCubit, SignUpUploadState>(
                buildWhen: (previous, current) =>
                    previous.pathIdFront != current.pathIdFront,
                builder: (context, state) {
                  return state.pathIdFront == ''
                      ? Column(
                          children: [
                            Text(getAccountLang(AccountKeyLang.ekycTitle1),
                                softWrap: true,
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                                style: TextStyleUtils.text16Weight500
                                    .copyWith(color: ColorUtils.gray900),
                                textAlign: TextAlign.start),
                            ItemDescriptionView(
                              title: getAccountLang(
                                  AccountKeyLang.ekycDescription1),
                            ),
                            ItemDescriptionView(
                              title: getAccountLang(
                                  AccountKeyLang.ekycDescription2),
                            ),
                            ItemDescriptionView(
                              title: getAccountLang(
                                  AccountKeyLang.ekycDescription3),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                  color: ColorUtils.gray300,
                                  borderRadius: BorderRadius.circular(8)),
                              child: Text(
                                  getAccountLang(
                                      AccountKeyLang.ekycDescription4),
                                  softWrap: true,
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                  style: TextStyleUtils.text13Weight500
                                      .copyWith(color: ColorUtils.gray900),
                                  textAlign: TextAlign.start),
                            )
                          ],
                        )
                      : const SizedBox();
                },
              ),
              BlocBuilder<SignUpUploadCubit, SignUpUploadState>(
                buildWhen: (previous, current) =>
                    previous.pathIdFront != current.pathIdFront ||
                    previous.pathIdBack != current.pathIdBack,
                builder: (context, state) {
                  return (state.pathIdFront != '' || state.pathIdBack != '')
                      ? Row(
                          children: [
                            Expanded(
                              child: ItemCCCD(
                                  typeUpload: UploadImageType.frontIDCard,
                                  pathImage: state.pathIdFront,
                                  title: getAccountLang(AccountKeyLang.font),
                                  onCallBack: (type) {}),
                            ),
                            kSpacingWidth16,
                            Expanded(
                              child: ItemCCCD(
                                  typeUpload: UploadImageType.backIDCard,
                                  pathImage: state.pathIdBack,
                                  title: getAccountLang(AccountKeyLang.behind),
                                  onCallBack: (type) {}),
                            ),
                            kSpacingHeight16,
                          ],
                        )
                      : const SizedBox();
                },
              ),
              kSpacingHeight16,
              // kSpacingHeight24,
              // BlocBuilder<SignUpUploadCubit, SignUpUploadState>(
              //   buildWhen: (previous, current) =>
              //       previous.pathIdBack != current.pathIdBack,
              //   builder: (context, state) {
              //     return _ItemUploadImage(
              //         typeUpload: UploadImageType.backIDCard,
              //         pathImage: state.pathIdBack,
              //         title: getAccountLang(AccountKeyLang.backIdentiCard),
              //         onCallBack: (type) {
              //           selectImage(type);
              //         });
              //   },
              // ),
              // kSpacingHeight24,
              BlocBuilder<SignUpUploadCubit, SignUpUploadState>(
                buildWhen: (previous, current) =>
                    previous.pathIdFront != current.pathIdFront,
                builder: (context, state) {
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      state.pathIdFront == ''
                          ? ButtonWidget(
                              colorEnable: ColorUtils.primary,
                              colorBorder: ColorUtils.bgPopup,
                              actionWidget: Row(
                                children: [
                                  SvgPicture.asset(
                                    AccountKeyAssets.icCamera,
                                    colorFilter: ColorFilter.mode(
                                        ColorDefine.white, BlendMode.srcIn),
                                  ),
                                  kSpacingWidth4,
                                  Text(
                                    getAccountLang(
                                      AccountKeyLang.startTakingPhotos,
                                    ),
                                    style: TextStyleUtils.text14Weight500
                                        .copyWith(color: ColorDefine.white),
                                  ),
                                ],
                              ),
                              onPressed: () async {
                                Map res = await SampleCallNativeFlutter.showOCR(
                                  '${Session().onboardingToken}',
                                  'Transcode',
                                  sl.get<StockBuildConfigs>().iamUrl() +
                                      OnboardingPathAPI.onboardingOCRV2,
                                  'vi',
                                  headers: {
                                    "x-device":
                                        await AppUtils.getFpjsDeviceId(),
                                    "x-devicetype":
                                        Platform.isAndroid ? "Android" : "IOS",
                                    "x-lang": "vi",
                                    "x-via": "Y",
                                  },
                                ) as Map;

                                var response = OCRResponse.fromJson(res);

                                if (response.shouldRequestPermission) {
                                  PermissionUtils.requestCameraPermission();

                                  return;
                                }

                                if (response.error?.type == 'UNKNOWN_ERROR') {
                                  popupWarning(
                                      navigation.navigatorKey.currentContext!,
                                      onBack: () => SignUpRouter()
                                          .onBackUtil(SignUpTypeScreen.phone));

                                  return;
                                }

                                final dataOCR = response.response;

                                if (dataOCR == null) return;

                                String pathFont = await bloc
                                    .saveBase64ToFile(res['front_image']);
                                bloc.handleCallBackCamera(
                                    UploadImageType.frontIDCard, pathFont,
                                    base64FrontImage: res['front_image']);
                                Future.delayed(const Duration(seconds: 1));
                                String pathBack = await bloc
                                    .saveBase64ToFile(res['back_image']);
                                bloc.handleCallBackCamera(
                                    UploadImageType.backIDCard, pathBack);
                                // bloc.handleCallBackCamera(
                                //     UploadImageType.signature, pathBack);
                                if (dataOCR.data == null) {
                                  showSnackBar(context, dataOCR.message ?? '');
                                  return;
                                }
                                bloc.goToInfoOcr(dataOCR.data ?? OCR());
                                // selectImage(UploadImageType.signature);
                              },
                            )
                          : ButtonWidget(
                              colorEnable: ColorUtils.primary,
                              colorBorder: ColorUtils.bgPopup,
                              actionWidget: Row(
                                children: [
                                  SvgPicture.asset(
                                    AccountKeyAssets.icSuccess,
                                    width: 16,
                                    height: 16,
                                    colorFilter: ColorFilter.mode(
                                        ColorDefine.white, BlendMode.srcIn),
                                  ),
                                  kSpacingWidth4,
                                  Text(
                                    getAccountLang(
                                        AccountKeyLang.takePhotosSuccessfully),
                                    style: TextStyleUtils.text14Weight500
                                        .copyWith(color: ColorDefine.white),
                                  ),
                                ],
                              ),
                            ),
                      const SizedBox()
                    ],
                  );
                },
              ),

              // // kSpacingHeight24,
              // const SignUpUploadGuideView()
            ],
          ),
        ),
      ),
    );
  }

  void selectImage(UploadImageType type) {
    if (checkDoubleTap) {
      return;
    }
    checkDoubleTap = true;
    switch (type) {
      case UploadImageType.frontIDCard:
      case UploadImageType.backIDCard:
        gotoCamera(type);
        break;
      case UploadImageType.signature:
        gotoCameraSignature();
        break;
    }
    checkDoubleTap = false;
  }

  Future gotoCamera(UploadImageType type) async {
    final context = navigation.navigatorKey.currentContext;
    if (context == null) {
      return;
    }
    final isSuccess = await SignUpPermissionUtils.checkPermissionCamera();
    if (isSuccess) {
      final cameras = await availableCameras();
      final camera = cameras.firstWhereOrNull(
          (element) => element.lensDirection == CameraLensDirection.back);

      if (cameras.isNotEmpty && camera != null) {
        RouterUtils.pushFullScreen(
            AccountCameraOcr(
                camera: camera,
                currentType: type == UploadImageType.frontIDCard
                    ? AccountTypeIDCard.front
                    : AccountTypeIDCard.back), onCallBack: (pathSave) {
          if (pathSave != null && pathSave is String) {
            bloc.handleCallBackCamera(type, pathSave);
          }
        });
      }
    }
  }

  Future<void> gotoCameraSignature() async {
    final context = navigation.navigatorKey.currentContext;
    if (context == null) {
      return;
    }
    final isSuccess = await SignUpPermissionUtils.checkPermissionCamera();
    if (isSuccess) {
      final cameras = await availableCameras();
      final camera = cameras.firstWhereOrNull(
          (element) => element.lensDirection == CameraLensDirection.back);

      if (cameras.isNotEmpty && camera != null) {
        RouterUtils.pushFullScreen(
            AppCameraPage(
              camera: camera,
              title: getAccountLang(AccountKeyLang.yourSignature),
              nameFileSave: 'IDCARD_SIGNATURE',
            ), onCallBack: (pathSave) {
          if (pathSave != null && pathSave is String) {
            bloc.handleCallBackCamera(UploadImageType.signature, pathSave);
          }
        });
      }
    }
  }
}

class _ItemUploadImage extends StatelessWidget {
  final UploadImageType typeUpload;
  final String pathImage;
  final String title;
  final Function(UploadImageType) onCallBack;

  const _ItemUploadImage(
      {Key? key,
      required this.typeUpload,
      required this.pathImage,
      required this.title,
      required this.onCallBack})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        onCallBack(typeUpload);
      },
      child: Column(
        children: [
          SizedBox(
              width: 120,
              height: 120,
              child: pathImage.isEmpty
                  ? getBgEmpty()
                  : Image.file(File(pathImage))),
          const SizedBox(width: 16),
          Text(title, style: TextStyleUtils.text16Weight600),
          const SizedBox(width: 16),
        ],
      ),
    );
  }

  Image getBgEmpty() {
    final map = {
      UploadImageType.frontIDCard.name: isDark()
          ? AccountKeyAssets.bgIdCardFrontDark
          : AccountKeyAssets.bgIdCardFront,
      UploadImageType.backIDCard.name: isDark()
          ? AccountKeyAssets.bgIdCardBackDark
          : AccountKeyAssets.bgIdCardBack,
      UploadImageType.signature.name: isDark()
          ? AccountKeyAssets.bgSignatureDark
          : AccountKeyAssets.bgSignature
    };
    return Image.asset(
      map[typeUpload.name] ?? AccountKeyAssets.uploadGuide,
    );
  }
}

class _ItemUploadSignatureImage extends StatelessWidget {
  final UploadImageType typeUpload;
  final String pathImage;
  final String title;
  final Function(UploadImageType) onCallBack;

  const _ItemUploadSignatureImage(
      {Key? key,
      required this.typeUpload,
      required this.pathImage,
      required this.title,
      required this.onCallBack})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        onCallBack(typeUpload);
      },
      child: Row(
        children: [
          SizedBox(
              width: 120,
              height: 120,
              child: pathImage.isEmpty
                  ? getBgEmpty()
                  : Image.file(File(pathImage))),
          const SizedBox(width: 16),
          Text(title, style: TextStyleUtils.text16Weight600),
          const SizedBox(width: 16),
        ],
      ),
    );
  }

  Image getBgEmpty() {
    final map = {
      UploadImageType.frontIDCard.name: isDark()
          ? AccountKeyAssets.bgIdCardFrontDark
          : AccountKeyAssets.bgIdCardFront,
      UploadImageType.backIDCard.name: isDark()
          ? AccountKeyAssets.bgIdCardBackDark
          : AccountKeyAssets.bgIdCardBack,
      UploadImageType.signature.name: isDark()
          ? AccountKeyAssets.bgSignatureDark
          : AccountKeyAssets.bgSignature
    };
    return Image.asset(
      map[typeUpload.name] ?? AccountKeyAssets.uploadGuide,
    );
  }
}
