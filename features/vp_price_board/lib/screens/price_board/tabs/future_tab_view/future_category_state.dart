part of 'future_category_cubit.dart';

class FutureCategoryTabState {
  FutureCategoryTabState({
    required this.apiStatus,
    this.marketInfo,
    this.sortMode,
    this.data,
  });

  final ApiStatus apiStatus;

  final List<StockInfoModel>? data;

  final MarketInfoModel? marketInfo;

  final SortMode? sortMode;

  List<StockInfoModel>? _sortedData;

  List<StockInfoModel>? get sortedData => _sortedData ?? data;

  FutureCategoryTabState copyWith({
    List<StockInfoModel>? data,
    MarketInfoModel? marketInfo,
    SortMode? sortMode,
    ApiStatus? apiStatus,
  }) {
    final state = FutureCategoryTabState(
      data: data ?? this.data,
      sortMode: sortMode ?? this.sortMode,
      apiStatus: apiStatus ?? this.apiStatus,
      marketInfo: marketInfo ?? this.marketInfo,
    );

    if (state.sortMode != null) {
      state._sortedData = state.data.sortBy(state.sortMode!);
    }

    return state;
  }
}
