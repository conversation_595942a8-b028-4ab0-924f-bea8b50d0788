import 'dart:async';

import 'package:app/common/error/show_error.dart';
import 'package:app/common/utils/app_config_utils.dart';
import 'package:app/common/utils/app_keyboard_utils.dart';
import 'package:app/common/utils/app_snackbar_utils.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/common/utils/account_string_utils.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/onboarding_repository.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/cubit/sign_up_step.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_tracking.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/i_sign_up_step.dart';
import 'package:core/di/injector.dart';
import 'package:flutter/material.dart';

import 'sign_up_acc_number_state.dart';

class SignUpAccNumberCubit extends ISignUpController<SignUpAccNumberState> {
  SignUpAccNumberCubit() : super(const SignUpAccNumberState());

  @override
  String? get title => getAccountLang(AccountKeyLang.accountNumber);

  @override
  int? get progress => SignUpStep.accNum.process;

  @override
  void init() {
    callApiSuggestCustId(false, isFirst: true);
  }

  @override
  bool isTypeScreen(SignUpTypeScreen value) {
    return value == SignUpTypeScreen.accNumber;
  }

  @override
  void onNext(SignUpTypeScreen type) {
    if (isTypeScreen(type)) {
      SignUpRouter().onPush(SignUpTypeScreen.contract);
    }
  }

  TextEditingController txtControllerAccNum = TextEditingController();

  void loading(bool isLoading) {
    emit(state.copyWith(isLoading: isLoading));
  }

  // Call API suggest custID
  void callApiSuggestCustId(bool random, {bool isFirst = false}) async {
    if (state.isLoading) {
      return;
    }
    if (!isFirst) {
      AppKeyboardUtils.dismissKeyboard();
    }
    loading(true);
    setEnable(false);
    try {
      final value = await sl.get<OnboardingRepository>().onboardingUsersAccNum(
          AccountStringUtils.getAccNum(SignUpData().mobilePhone),
          isRandom: random);

      if (value != null) {
        if (value.isSuccess()) {
          txtControllerAccNum.clear();
          txtControllerAccNum.text = AccountStringUtils.getAccNum(
              value.data?.number ?? SignUpData().mobilePhone);
        } else {
          showMessage(value.message);
        }
        return;
      }
    } catch (e) {
      showError(e);
    } finally {
      loading(false);
    }
  }

  // Call API kiem tra so tai khoan da ton tai hay chua
  Future<void> callAPICheckExitsAccNum() async {
    if (state.isLoading) {
      return;
    }
    AppKeyboardUtils.dismissKeyboard();
    final custCd = '${AppConfigUtils.preAccNum}${txtControllerAccNum.text}';
    loading(true);
    try {
      final value =
          await sl.get<OnboardingRepository>().onboardingUsersAccount(custCd);
      if (value != null) {
        setEnable(value.isSuccess());
        if (!value.isSuccess()) {
          showMessage(value.message);
          SignUpTracking().signupAccountNumFail(value.message);
        } else {
          SignUpTracking().signupAccountNumSuccess();
        }
      }
    } catch (e) {
      showError(e);
      SignUpTracking().signupAccountNumFail(e);
    } finally {
      loading(false);
    }
  }

  bool valid(String value) {
    return value.length == 6;
  }
}
