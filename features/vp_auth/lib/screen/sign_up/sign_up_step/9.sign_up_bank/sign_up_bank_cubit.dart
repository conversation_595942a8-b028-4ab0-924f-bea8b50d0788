import 'dart:async';

import 'package:app/common/error/show_error.dart';
import 'package:app/common/extensions/string_extensions.dart';
import 'package:app/common/utils/app_config_utils.dart';
import 'package:app/common/utils/app_launching_utils.dart';
import 'package:app/common/utils/app_log_utils.dart';
import 'package:app/common/utils/app_snackbar_utils.dart';
import 'package:app/common/widgets/vpbank_loading.dart';
import 'package:app/packages/centralize_OTP/smart_otp/rx_core.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/data/model/register/account_bank_branch.dart';
import 'package:app/packages/shared/modules/account/data/model/register/account_bank_list.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/models/onboarding_users_banks_obj.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/onboarding_repository.dart';
import 'package:app/packages/shared/modules/account/domain/account_register_repository.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/cubit/sign_up_step.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_tracking.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/9.sign_up_bank/widget/popup_confirm_open_back_account.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/9.sign_up_bank/widget/popup_warning_exist_bank_account.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/i_sign_up_step.dart';
import 'package:app/packages/shared/modules/partner_connection/common/lang/partner_connection_key_lang.dart';
import 'package:app/packages/shared/modules/partner_connection/common/lang/partner_connection_localized_value.dart';
import 'package:app/packages/shared/modules/partner_connection/presentation/feature/open_account/bloc/open_account_bloc_cubit.dart';
import 'package:app/packages/shared/modules/partner_connection/presentation/feature/open_account/widget/terms_and_conditions_bottom_sheet.dart';
import 'package:app/packages/stock/modules/place_order/presentation/feature/place_order/bloc/place_order_bloc_map_import.dart';
import 'package:flutter/material.dart';
import 'package:tiengviet/tiengviet.dart';

import 'package:app/packages/shared/modules/partner_connection/presentation/feature/open_account/widget/dialog_error.dart';

part 'sign_up_bank_state.dart';

class SignUpBankCubit extends ISignUpController<SignUpBankState> {
  SignUpBankCubit() : super(SignUpBankStateInitial());

  @override
  String? get title => getAccountLang(AccountKeyLang.bankInfo);

  @override
  int? get progress => SignUpStep.bank.process;

  Timer? _debounce;

  //default = true
  late RxBool openBankAccSubject = RxBool(SignUpData().afChanel.isNullOrEmpty);

  OnboardingRepository get repository => sl.get<OnboardingRepository>();
  bool accept = false;

  @override
  void init() {
    listenerTxt();

    txtAccountNumber.text = SignUpData().bankData.bankAccountNumber;

    txtAccountName.text = SignUpData().bankData.bankAccountName;

    setBankNameSelect(SignUpData().bankData.bank, resetBankBranch: false);

    setBankBranchSelect(SignUpData().bankData.bankBranch);

    setEnable(valid());
  }

  @override
  bool isTypeScreen(SignUpTypeScreen value) {
    return value == SignUpTypeScreen.bank;
  }

  @override
  void onNext(SignUpTypeScreen type) {
    if (isTypeScreen(type)) {
      if (accept) {
        popupConfirmOpenBackAccount(
            navigation.navigatorKey.currentContext!,
            () => showTermsAndConditionsBottomSheet(
                    navigation.navigatorKey.currentContext!,
                    () => onViewTransactionCondition(),
                    () => onViewContract(), () async {
                  navigation.goBack();
                  final obj = SignUpData().bankData;
                  final param = OnboardingUsersBankRequestObj(
                    bankCode: obj.bank!.bankid.toString(),
                    bankName: obj.bank!.bankname.toString(),
                    bankAccountName: obj.bankAccountName,
                    bankAccountNo: obj.bankAccountNumber,
                    bankBranchCode: obj.bankBranch!.branchid.toString(),
                    bankBranchName: obj.bankBranch!.branchcode.toString(),
                  );
                  try {
                    showLoading();
                    final value = await repository.onboardingUsersBank(param);
                    await repository.agreeOpenAccountBank(openBankAcc: true);
                    hideLoading();
                    if (value != null) {
                      if (value.isSuccess()) {
                        SignUpRouter().onPush(SignUpTypeScreen.moneyLaundering);
                      } else {
                        showMessage(value.message);
                        SignUpTracking().signupBankInfoFail(value.message);
                      }
                    }
                  } catch (e) {
                    hideLoading();
                    SignUpTracking().signupBankInfoFail(e);
                    showError(e);
                  }
                }));
        return;
      }
      callApiUpdateBankAcc();
    }
  }

  TextEditingController txtAccountNumber = TextEditingController();
  TextEditingController txtAccountName = TextEditingController();

  void listenerTxt() {
    txtAccountNumber.addListener(() {
      emit(SignUpBankStateAccNoBank(msgErrAccNoBank: null));
      SignUpData().bankData.setBankAccountNumber(txtAccountNumber.text);
      setEnable(valid());
    });

    txtAccountName.addListener(() {
      if (_debounce?.isActive ?? false) {
        _debounce!.cancel();
      }
      emit(SignUpBankStateAccNameBank(msgErrAccNameBank: null));
      _debounce =
          Timer(const Duration(milliseconds: AppConfigUtils.debounce), () {
        emit(SignUpBankStateAccNameBank(
            msgErrAccNameBank: validBankAccountName()
                ? null
                : 'Tên không trùng khớp với giấy tờ CCCD'));

        SignUpData().bankData.setBankAccountName(txtAccountName.text.trim());
        setEnable(valid());
      });
    });
  }

  /* ----------------- BANK -------------------- */
  StreamController controlerBank = StreamController();

  Stream get streamBank => controlerBank.stream;

  // API getBankList
  List<AccountBankListResponse> listBank = [];

  AccountRegisterRepository get accRepository =>
      sl.get<AccountRegisterRepository>();

  Future<void> getBankList(BuildContext context) async {
    try {
      showLoading();
      listBank = await accRepository.getListBank();
    } catch (e) {
      showError(e);
    } finally {
      hideLoading();
    }
  }

  AccountBankListResponse? bankSelected;

  void setBankNameSelect(AccountBankListResponse? bank,
      {bool resetBankBranch = true}) {
    if (bank == null) return;

    bankSelected = bank;

    SignUpData().bankData.setBank(bank);

    controlerBank.sink.add(bank.bankcode);

    if (resetBankBranch) setBankBranchSelect(null);

    if (bank.isVPBank) onChangeStatusRegister(false);

    setEnable(valid());
  }

  /* ----------------- CHI NHANH BANK -------------------- */
  // Controller bank chi nhanh
  StreamController<AccountBankBranchResponseObj?> controlerBankBranch =
      StreamController<AccountBankBranchResponseObj?>();

  Stream<AccountBankBranchResponseObj?> get streamBankBranch =>
      controlerBankBranch.stream;

  // Call API lay danh sach chi nhanh ngan hang
  List<AccountBankBranchResponseObj> listBankBranch = [];

  Future<void> callApiGetBankBranch(int bankId) async {
    try {
      showLoading();

      listBankBranch.clear();

      listBankBranch = await accRepository.getListBankBranch(bankId);
    } catch (e) {
      showError(e);
    } finally {
      hideLoading();
    }
  }

  // Hien thi  bank chi nhanh da chon
  AccountBankBranchResponseObj? bankBranchSelect;

  void setBankBranchSelect(AccountBankBranchResponseObj? bankBranch) {
    bankBranchSelect = bankBranch;
    controlerBankBranch.sink.add(bankBranchSelect);
    SignUpData().bankData.setBankBranch(bankBranchSelect);
    setEnable(valid());
  }

  // Kiem tra de enable button hay khong
  bool valid() {
    return txtAccountNumber.text.isNotEmpty &&
        validBankAccountName() &&
        bankSelected != null &&
        bankBranchSelect != null;
  }

  bool validBankAccountName() {
    final txtValue = TiengViet.parse(txtAccountName.text).toLowerCase().trim();

    final fullNameOcr =
        TiengViet.parse(SignUpData().ocrData.fullname).toLowerCase().trim();

    return txtAccountName.text.isNotEmpty && txtValue == fullNameOcr;
  }

  @override
  Future<void> close() {
    try {
      controlerBank.close();
      controlerBankBranch.close();
      txtAccountNumber.dispose();
      txtAccountName.dispose();
      openBankAccSubject.close();
      if (_debounce?.isActive ?? false) {
        _debounce!.cancel();
      }
    } catch (e) {
      dlog(e);
    }
    return super.close();
  }

  // Call API update bank
  Future callApiUpdateBankAcc() async {
    final obj = SignUpData().bankData;
    final param = OnboardingUsersBankRequestObj(
      bankCode: obj.bank!.bankid.toString(),
      bankName: obj.bank!.bankname.toString(),
      bankAccountName: obj.bankAccountName,
      bankAccountNo: obj.bankAccountNumber,
      bankBranchCode: obj.bankBranch!.branchid.toString(),
      bankBranchName: obj.bankBranch!.branchcode.toString(),
    );
    showLoading();
    try {
      final value = await repository.onboardingUsersBank(param);

      await repository.agreeOpenAccountBank(openBankAcc: false);

      if (value != null) {
        if (value.isSuccess()) {
          SignUpTracking().signupBankInfoSuccess();
          SignUpRouter().onPush(SignUpTypeScreen.accNumber);
        } else {
          showMessage(value.message);
          SignUpTracking().signupBankInfoFail(value.message);
        }
      }
    } catch (e) {
      SignUpTracking().signupBankInfoFail(e);
      showError(e);
    } finally {
      hideLoading();
    }
  }

  void onBannerClick() {
    const defaultLink = 'https://www.vpbank.com.vn/uu-dai?f=Ngan-hang-so';
    openBrowser(defaultLink);
  }

  void onChangeStatusRegister(bool status) {
    openBankAccSubject.value = status;
  }

  void onViewTransactionCondition() {
    const defaultLink =
        'https://www.vpbank.com.vn/-/media/vpbank-latest/tai-lieu-bieu-mau/bieu-mau/khcn/Dkgd-chung-ve-cung-cap-va-su-dung-dich-vu-phi-tin-dung-ap-dung-doi-voi-KHCN-tai-VPBank';
    openBrowser(
      defaultLink,
    );
  }

  void onViewContract() async {
    try {
      showLoading();

      final urlFileContract =
          await sl.get<OnboardingRepository>().getUrlFileContractRegisterBank();

      if (urlFileContract.isNotEmpty) {
        openBrowser(urlFileContract);
      }
    } catch (e) {
      showError(e);
    } finally {
      hideLoading();
    }
  }

  Future<void> existenceBank() async {
    try {
      showDialogLoading();
      final data =
          await sl.get<OnboardingRepository>().checkVpbankAccountExist();
      hideDialogLoading();
      if (data.code == EXISTENCE.IABNOT671.name && data.isSuccess()) {
        //Tài khoản khả dụng
        showTermsAndConditionsBottomSheet(
            navigation.navigatorKey.currentContext!,
            () => onViewTransactionCondition(),
            () => onViewContract(), () async {
          navigation.goBack();
          SignUpRouter().onPush(SignUpTypeScreen.moneyLaundering);
        });
      }
      if (data.code == EXISTENCE.IABERR681.name) {
        //Lỗi kết nối với vpbank show toast
        showErrorMessage(data.message);
      }
      if (data.code == EXISTENCE.IABERR19.name) {
        showDialogError(
            navigation.navigatorKey.currentContext!,
            getPartnerConnectionLang(PartnerConnectionLangKey.notification),
            getPartnerConnectionLang(PartnerConnectionLangKey.msgOne),
            //Hiện tại VPBank chưa thể cung cấp dịch vụ cho bạn. Vui lòng liên hệ tổng đài chăm sóc khách hàng ********** để được hỗ trợ. Ấn “Đã hiểu” để trở về.
            pressRight: () {
          navigation.goBack();
        });
        return;
      }
      if (data.code == EXISTENCE.IABNOT672.name) {
        //Message của Vpbank trả về
        popupWarningExistBankAccount(navigation.navigatorKey.currentContext!);
        return;
      }
      if (data.code == EXISTENCE.IABERR679.name) {
        showErrorMessage(data.message);
        return;
      }
    } catch (e) {
      hideDialogLoading();
      showError(e);
    }
  }
}
