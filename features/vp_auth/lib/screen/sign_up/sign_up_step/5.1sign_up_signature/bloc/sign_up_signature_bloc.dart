import 'package:app/common/utils/app_log_utils.dart';
import 'package:app/common/widgets/vpbank_loading.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/onboarding_repository.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_tracking.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/i_sign_up_step.dart';
import 'package:core/di/injector.dart';

part 'sign_up_signature_state.dart';

class SignUpSignatureCubit extends ISignUpController<SignUpSignatureState> {
  SignUpSignatureCubit() : super(const SignUpSignatureState());

  @override
  bool isTypeScreen(SignUpTypeScreen value) {
    return value == SignUpTypeScreen.signature;
  }

  @override
  void init() {
    final obj = SignUpData().attachDataObj;

    emit(
      state.copyWith(
        pathSignature: obj.getSignature(),
      ),
    );

    setEnable(obj.getSignature() != '');
  }

  @override
  String? get title => 'Chụp ảnh chữ ký';

  // @override
  // int? get progress => SignUpStep..process;

  @override
  void onNext(SignUpTypeScreen type) {
    if (isTypeScreen(type)) {
      uploadSignature();
    }
  }

  Future uploadSignature() async {
    final objIdAttach = SignUpData().attachDataObj;

    showDialogLoading();
    dlog(objIdAttach.getSignature());

    try {
      dlog('sssss');
      final data = await sl
          .get<OnboardingRepository>()
          .uploadSignature(apiPath: objIdAttach.getSignature());
      dlog('kkkkk $data');

      if (data?.isSuccess() ?? false) {
        SignUpRouter().onPush(SignUpTypeScreen.bank);
      }
      hideDialogLoading();
    } catch (e) {
      hideDialogLoading();
    }
  }

  void handleCallBackCamera(String pathSave) {
    SignUpTracking().signupSignatureSuccess();
    SignUpData().attachDataObj.setSignature(pathSave);
    emit(state.copyWith(pathSignature: pathSave));
    dlog('ssss');
    dlog(SignUpData().attachDataObj.getSignature());
    setEnable(SignUpData().attachDataObj.getSignature() != '');
  }
}
