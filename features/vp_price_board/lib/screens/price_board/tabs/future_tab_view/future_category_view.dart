import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_price_board/screens/price_board/tabs/future_tab_view/future_category_cubit.dart';
import 'package:vp_price_board/vp_price_board_navigator.dart';
import 'package:vp_price_board/widget/fu_stock_list_item.dart';
import 'package:vp_price_board/widget/sort/future_sort_view.dart';
import 'package:vp_price_board/widget/stock_list_item.dart';
import 'package:vp_price_board/widget/stock_list_item_builder_view.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

class FutureCategoryView extends StatefulWidget {
  const FutureCategoryView({required this.stockType, super.key});

  final StockType stockType;

  @override
  State<FutureCategoryView> createState() => _FutureCategoryViewState();
}

class _FutureCategoryViewState extends State<FutureCategoryView>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocProvider(
      create:
          (_) =>
              FutureCategoryTabCubit(stockType: widget.stockType)..loadData(),
      child: BlocBuilder<FutureCategoryTabCubit, FutureCategoryTabState>(
        builder: (_, state) {
          return StockListItemBuilderView(
            apiStatus: state.apiStatus,
            data: state.sortedData,
            onRetry: () => onRefresh(context),
            builder: (context, data) {
              return FutureSortView(
                onChanged:
                    (_, mode) =>
                        context.read<FutureCategoryTabCubit>().sortBy(mode),
                sortType: state.sortMode?.sortType,
                status: state.sortMode?.status,
                builder: (context, columns) {
                  return FuStockListView(
                    items: data,
                    columns: columns,
                    marketInfo: state.marketInfo,
                    onRefresh: () => onRefresh(context),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }

  Future onRefresh(BuildContext context) =>
      context.read<FutureCategoryTabCubit>().onRefresh();

  @override
  bool get wantKeepAlive => true;
}
