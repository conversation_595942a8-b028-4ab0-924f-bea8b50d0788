import 'dart:async';

import 'package:app/common/error/show_error.dart';
import 'package:app/common/utils/app_keyboard_utils.dart';
import 'package:app/common/utils/app_log_utils.dart';
import 'package:app/common/utils/app_snackbar_utils.dart';
import 'package:app/common/widgets/vpbank_loading.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/models/onboarding_users_update_ocr_obj.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/onboarding_repository.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_tracking.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_utils.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/7.sign_up_info_edit/helper/sign_up_info_edit_validator.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/i_sign_up_step.dart';
import 'package:core/di/injector.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

part 'sign_up_info_edit_state.dart';

class SignUpInfoEditCubit extends ISignUpController<SignUpInfoEditState> {
  SignUpInfoEditCubit() : super(SignUpInfoEditState());

  @override
  String? get title => getAccountLang(AccountKeyLang.editInfo);

  @override
  void init() {
    fillDataOCR();
  }

  @override
  bool isTypeScreen(SignUpTypeScreen value) {
    return value == SignUpTypeScreen.editInfoOcr;
  }

  @override
  void onNext(SignUpTypeScreen type) {
    if (isTypeScreen(type)) {
      apiUpdateUsersOCR();
    }
  }

  @override
  Future<void> close() {
    try {
      txtFullName.dispose();
      txtIdentifyCard.dispose();
      txtPlaceOfIssuedCard.dispose();
      txtPermanentAddress.dispose();
      txtCurrentAddress.dispose();
    } catch (e) {
      dlog(e.toString());
    }
    return super.close();
  }

  TextEditingController txtFullName = TextEditingController();
  TextEditingController txtIdentifyCard = TextEditingController();
  TextEditingController txtPlaceOfIssuedCard = TextEditingController();
  TextEditingController txtPermanentAddress = TextEditingController();
  TextEditingController txtCurrentAddress = TextEditingController();

  void fillDataOCR() {
    final obj = SignUpData().ocrData;
    txtFullName.text = obj.fullname;
    txtIdentifyCard.text = obj.idcode;
    txtPlaceOfIssuedCard.text = obj.idplace;
    txtPermanentAddress.text = obj.address;
    txtCurrentAddress.text = obj.address2;
  }

  String selectGender = SignUpData().ocrData.sex;

  int getGender() {
    return SignUpData().ocrData.sex == SignUpUtils.feMale ? 2 : 1;
  }

  bool valid(BuildContext context) {
    final validFullName =
        SignUpInfoEditValidator.validFullName(txtFullName.text).isEmpty;
    final validIdCard =
        SignUpInfoEditValidator.validIdentifyCard(txtIdentifyCard.text).isEmpty;
    final validIssuePlace =
        SignUpInfoEditValidator.validIssuePlace(txtPlaceOfIssuedCard.text)
            .isEmpty;
    final validPermanentAddress =
        SignUpInfoEditValidator.validPermanentAddress(txtPermanentAddress.text)
            .isEmpty;
    final validCurrentAddress =
        SignUpInfoEditValidator.validCurrentAddress(txtCurrentAddress.text)
            .isEmpty;
    return validFullName &&
        validIdCard &&
        validIssuePlace &&
        validPermanentAddress &&
        validCurrentAddress;
  }

  apiUpdateUsersOCR() async {
    try {
      AppKeyboardUtils.unFocusTextField();
      showDialogLoading();
      final param = OnboardingUsersUpdateOcrRequestObj(
          address: txtCurrentAddress.text, gender: selectGender);
      final value =
          await sl.get<OnboardingRepository>().onboardingUsersOcr(param);

      if (value != null) {
        if (value.isSuccess()) {
          final obj = SignUpData().ocrData;
          obj.fullname = txtFullName.text;
          obj.idcode = txtIdentifyCard.text;
          obj.idplace = txtPlaceOfIssuedCard.text;
          obj.address = txtPermanentAddress.text;
          obj.address2 = txtCurrentAddress.text;
          obj.sex = selectGender;
          SignUpData().dataOCR.gioiTinh = obj.sex;
          SignUpData().dataOCR.diachilienhe = obj.address2;
          SignUpRouter().onBack();
          SignUpTracking().signupProfileInfoEditSuccess();
        } else {
          showMessage(value.message);
        }
      }
    } catch (e) {
      showError(e);
    } finally {
      hideDialogLoading();
    }
  }
}
