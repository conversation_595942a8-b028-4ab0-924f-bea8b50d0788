import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_price_board/vp_price_board_navigator.dart';
import 'package:vp_price_board/widget/sort/normal_sort_view.dart';
import 'package:vp_price_board/widget/stock_list_item.dart';
import 'package:vp_price_board/widget/stock_list_item_builder_view.dart';
import 'package:vp_stock_common/screens/watchlist/watchlist_bloc.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

import 'follow_tab_bloc.dart';

class FollowTabView extends StatelessWidget {
  const FollowTabView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => FollowTabCubit()..loadWatchList(),
      child: BlocListener<WatchlistBloc, WatchlistState>(
        listenWhen: (pState, state) {
          if (pState.watchlists.isNullOrEmpty &&
              state.watchlists.isNullOrEmpty) {
            return false;
          }

          return pState.watchlists != state.watchlists;
        },
        listener: (context, state) {
          context.read<FollowTabCubit>().onWatchListChanged(state.watchlists);
        },
        child: const _FollowTabBody(),
      ),
    );
  }
}

class _FollowTabBody extends StatefulWidget {
  const _FollowTabBody();

  @override
  State<_FollowTabBody> createState() => _FollowTabBodyState();
}

class _FollowTabBodyState extends State<_FollowTabBody>
    with AutomaticKeepAliveClientMixin {
  ScrollController controller = ScrollController();

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        BlocSelector<FollowTabCubit, FollowTabState, WatchlistModel?>(
          selector: (state) => state.watchlist,
          builder: (context, watchlist) {
            return Padding(
              padding: const EdgeInsets.fromLTRB(12, 8, 0, 0),
              child: Row(
                children: [
                  Expanded(
                    child: VPDropdownView.small(
                      hint: watchlist?.name ?? 'Chọn danh mục',
                      onTap: () => openWatchlistSelectorBottomSheet(context),
                    ),
                  ),

                  const SizedBox(width: 12),

                  if (watchlist?.isWatchlistCreated == true)
                    IconButton(
                      onPressed:
                          () => openAddSymbolsToWatchlistBottomSheet(
                            context,
                            watchlist!,
                          ),
                      icon: SizedBox(
                        width: 32,
                        height: 32,
                        child: DecoratedBox(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: vpColor.backgroundElevation1,
                            border: Border.all(color: vpColor.strokeNormal),
                          ),
                          child: Icon(Icons.add, color: vpColor.iconPrimary),
                        ),
                      ),
                    ),

                  if (watchlist != null)
                    IconButton(
                      onPressed:
                          () => openWatchlistDetailBottomSheet(
                            context,
                            watchlist,
                          ),
                      icon: SizedBox(
                        width: 32,
                        height: 32,
                        child: DecoratedBox(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: vpColor.backgroundElevation1,
                            border: Border.all(color: vpColor.strokeNormal),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(8),
                            child: CommonAssets.icons.edit.icEdit.svg(
                              color: vpColor.iconPrimary,
                              width: 16,
                              height: 16,
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            );
          },
        ),

        Expanded(
          child: BlocBuilder<FollowTabCubit, FollowTabState>(
            builder: (context, state) {
              return StockListItemBuilderView(
                apiStatus: state.apiStatus,
                data: state.items,
                onRetry: () => onRefresh(context),
                builder: (context, data) {
                  return NormalSortView(
                    onChanged: onSort,
                    sortType: state.sortType,
                    status: state.sortStatus,
                    builder: (context, columns) {
                      return StockListView(
                        items: data,
                        controller: controller,
                        onRefresh: () => onRefresh(context),
                        columns: columns,
                      );
                    },
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Future onRefresh(BuildContext context) =>
      context.read<FollowTabCubit>().onRefresh();

  void openWatchlistDetailBottomSheet(
    BuildContext context,
    WatchlistModel watchlist,
  ) async {
    await priceBoardNavigator.openWatchlistDetailBottomSheet(
      context,
      watchlist: watchlist,
    );
  }

  void openAddSymbolsToWatchlistBottomSheet(
    BuildContext context,
    WatchlistModel watchlist,
  ) async {
    await priceBoardNavigator.openAddSymbolsToWatchlistBottomSheet(
      context,
      watchlist: watchlist,
      stockTypes: StockType.values.where((e) => e.isStock).toList(),
    );
  }

  Future openWatchlistSelectorBottomSheet(BuildContext context) async {
    final result = await priceBoardNavigator.openWatchlistSelectorBottomSheet(
      context,
      showAddWatchlistButton: true,
      showSuggestionWatchlist: true,
      showHoldingWatchlist: true,
    );

    if (result is WatchlistModel && context.mounted) {
      context.read<FollowTabCubit>().onWatchListSelected(result);
    }
  }

  void onSort(BuildContext context, SortMode mode) {
    context.read<FollowTabCubit>().sortBy(mode);

    controller.jumpTo(0);
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;
}
