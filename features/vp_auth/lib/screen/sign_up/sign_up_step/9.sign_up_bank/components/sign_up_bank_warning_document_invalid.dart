import 'dart:async';

import 'package:app/common/assets/common_key_assets.dart';
import 'package:app/global/navigator/app_router.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_ui/widgets/design_system/dialog/notifty_dialog.dart';
import 'package:flutter/material.dart';

Future showDialogWarningDocumentInValid(
  BuildContext context, {
  bool barrierDismissible = true,
}) {
  return showNotifyDialog(
    context: context,
    barrierDismissible: false,
    image: CommonKeyAssets.icWarning,
    colorButtonRight: ColorUtils.primary,
    title: getAccountLang(AccountKeyLang.notification),
    titleStyle: TextStyleUtils.text20Weight700.copyWith(
      color: ColorUtils.gray700,
    ),
    imagePadding: const EdgeInsets.only(bottom: 12),
    textButtonRight: getAccountLang(AccountKeyLang.continueText),
    colorBorderButtonLeft: ColorUtils.gray700,
    content: getAccountLang(AccountKeyLang.documentInValid),
    onPressedLeft: () async {
      navigation.goBack();

      await Future.delayed(const Duration(milliseconds: 200));

      SignUpRouter().onBackUtil(SignUpTypeScreen.upload);
    },
    textStyleLeft:
        TextStyleUtils.text14Weight500.copyWith(color: ColorUtils.black),
    textButtonLeft: getAccountLang(AccountKeyLang.takePhotoIdCard),
    onPressedRight: () {
      navigation.goBack();
      Timer(const Duration(milliseconds: 200), () {
        SignUpRouter().onPush(SignUpTypeScreen.accNumber);
      });
    },
  );
}
