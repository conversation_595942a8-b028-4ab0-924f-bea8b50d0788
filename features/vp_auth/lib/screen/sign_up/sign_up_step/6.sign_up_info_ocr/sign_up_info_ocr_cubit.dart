import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/cubit/sign_up_step.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_ocr_data.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_tracking.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/i_sign_up_step.dart';

part 'sign_up_info_ocr_state.dart';

class SignUpInfoOcrCubit extends ISignUpController<SignUpInfoOcrState> {
  SignUpInfoOcrCubit() : super(const SignUpInfoOcrState());

  @override
  String? get title => getAccountLang(AccountKeyLang.confirmInfo);

  @override
  int? get progress => SignUpStep.info.process;

  @override
  void onResume() {
    super.onResume();
    emit(state.copyWith(obj: SignUpData().ocrData));
  }

  @override
  bool isTypeScreen(SignUpTypeScreen value) {
    return value == SignUpTypeScreen.infoOcr;
  }

  @override
  void onNext(SignUpTypeScreen type) {
    if (isTypeScreen(type)) {
      SignUpRouter().onPush(SignUpTypeScreen.ekyc);
      SignUpTracking().signupProfileConfirmSuccess();
    }
  }

  @override
  bool get enable => true;
}
