import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'components/sign_up_captcha_view.dart';
import 'sign_up_phone_bloc.dart';

class SignUpPhonePage extends StatelessWidget {
  const SignUpPhonePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => SignUpPhoneBloc(),
      child: const _BodySignUpPhonePage(),
    );
  }
}

class _BodySignUpPhonePage extends StatefulWidget {
  const _BodySignUpPhonePage({Key? key}) : super(key: key);

  @override
  State<_BodySignUpPhonePage> createState() => _BodySignUpPhonePageState();
}

class _BodySignUpPhonePageState extends State<_BodySignUpPhonePage> {
  FocusNode myFocusNode = FocusNode();
  final controllerScrollView = ScrollController();
  String? sessionId;

  @override
  void dispose() {
    myFocusNode.dispose();
    controllerScrollView.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(getAccountLang(AccountKeyLang.enterPhoneNumber),
              style: TextStyleUtils.text16Weight600
                  .copyWith(color: ColorUtils.black)),
          const SizedBox(height: 12),
          BlocBuilder<SignUpPhoneBloc, SignUpPhoneState>(
            builder: (context, state) {
              final defaultType = state.validTextFieldType ==
                  SignUpPhoneValidTextFieldType.defaultType;
              final validType = state.validTextFieldType ==
                  SignUpPhoneValidTextFieldType.valid;
              return Form(
                key: context.read<SignUpPhoneBloc>().phoneKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppTextFormField(
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp('[0-9]'))
                      ],
                      contentPadding: const EdgeInsets.symmetric(
                          vertical: 12, horizontal: 16),
                      focusNode: myFocusNode,
                      autoFocus: true,
                      inputType: TextInputType.number,
                      prefix: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: SvgPicture.asset(
                          AccountKeyAssets.phone,
                          width: Icon16,
                          height: Icon16,
                          color: Theme.of(context).iconTheme.color,
                        ),
                      ),
                      hint: getAccountLang(AccountKeyLang.phoneNumber),
                      textEditingController:
                          context.read<SignUpPhoneBloc>().txtPhoneNumber,
                      maxLength: 20,
                      fillColor: defaultType
                          ? ColorUtils.bgInput
                          : (validType
                              ? ColorUtils.primary.withOpacity(0.16)
                              : ColorUtils.errorBgInput),
                      onChanged:
                          context.read<SignUpPhoneBloc>().onChangeTextField,
                      validator:
                          context.read<SignUpPhoneBloc>().onValidTexField,
                      errorStyle: vpTextStyle.body14?.copyWith(
                        color: ColorUtils.red,
                      ),
                      colorBorder: defaultType
                          ? null
                          : (validType ? ColorUtils.primary : ColorUtils.red),
                    ),
                    (state.msgTextField?.isNotEmpty ?? false)
                        ? Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child: Text(
                              state.msgTextField!,
                              style: vpTextStyle.body14?.copyWith(
                                color: defaultType
                                    ? ColorUtils.gray500
                                    : (validType
                                        ? ColorUtils.primary
                                        : ColorUtils.red),
                              ),
                            ),
                          )
                        : const SizedBox()
                  ],
                ),
              );
            },
          ),
          const SizedBox(height: 24),
          Expanded(
              child: SingleChildScrollView(
            controller: controllerScrollView,
            child: _CaptchaView(controller: controllerScrollView),
          ))
        ],
      ),
    );
  }
}

class _CaptchaView extends StatelessWidget {
  final ScrollController controller;

  const _CaptchaView({Key? key, required this.controller}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<SignUpPhoneBloc>();
    return Stack(children: [
      SignUpCaptchaView(
          textEditingController: bloc.txtPhoneNumber,
          onCallBack: (result) async {
            await controller.animateTo(0,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeIn);
            if (result.isNotEmpty) {
              bloc.setEnable(true);
              SignUpData().setMobilePhone(bloc.txtPhoneNumber.text);
              SignUpRouter().onPush(SignUpTypeScreen.otp, value: result);
              SignUpTracking().signupPhoneNumSuccess();
            } else {
              showMessage(getCommonLang(CommonKeyLang.errDefault));
            }
          },
          onCancelLoading: () {
            bloc.setEnable(false);
            bloc.loading(false);
          }),
      _LoadingView()
    ]);
  }

  String _getSessionID(dynamic data) {
    String sessionId = '';
    if (data is Map) {
      sessionId = data[KeyParam.sessionId];
    }
    return sessionId;
  }
}

class _LoadingView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SignUpPhoneBloc, SignUpPhoneState>(
      buildWhen: (previous, current) => previous.isLoading != current.isLoading,
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(child: VPBankLoading());
        }
        return const SizedBox.shrink();
      },
    );
  }
}

void _maintenanceSystem(String? content) {
  final context = navigation.navigatorKey.currentContext;
  if (context == null) return;
  return DialogHelper().show(
    context,
    DialogWidget.custom(
      closable: false,
      child: DialogSystemMaintenance(content: content),
    ),
  );
}
