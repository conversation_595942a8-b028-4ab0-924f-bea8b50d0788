
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_auth/generated/l10n.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';

import 'sign_up_referrer_cubit.dart';
import 'sign_up_referrer_state.dart';

class RegisterReferencePage extends StatefulWidget {
  const RegisterReferencePage({Key? key}) : super(key: key);

  @override
  State<RegisterReferencePage> createState() => RegisterReferencePageState();
}

class RegisterReferencePageState extends State<RegisterReferencePage> {
  final cubit = RegisterReferenceCubit();

  final pinCodeController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(S.current.account_type_user_reference,
                style: TextStyleUtils.text16Weight600
                    .copyWith(color: ColorUtils.black)),
            const SizedBox(height: 16),
            SizedBox(
              height: 40,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(width: 12),
                  Text(
                    AppConfigUtils.preAccNum,
                    style: TextStyleUtils.text16Weight600
                        .copyWith(color: ColorUtils.gray500),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: AppPincodeWidget(
                      controller: pinCodeController,
                      count: AppConfigUtils.appOTP,
                      onCompleted: (value) {},
                      autoDisposeControllers: false,
                      onChanged: (value, count) => cubit.onTextChanged(value),
                    ),
                  ),
                ],
              ),
            ),
            BlocSelector<RegisterReferenceCubit, RegisterReferenceState,
                    String?>(
                bloc: cubit,
                selector: (state) => state.responseText,
                builder: (_, responseText) {
                  if (responseText.isNullOrEmpty) {
                    return const SizedBox(height: 24);
                  }

                  final valid = cubit.verifyValid;

                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    child: valid
                        ? Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(
                                    text:
                                        '${S.current.account_user_reference}: ',
                                    style: vpTextStyle.body14?
                                        .copyWith(color: ColorUtils.gray900)),
                                TextSpan(
                                    text: responseText,
                                    style: vpTextStyle.body14?
                                        .copyWith(color: ColorUtils.primary)),
                              ],
                            ),
                          )
                        : Text(
                            responseText!,
                            style: vpTextStyle.body14.copyColor(vpColor.red),
                          ),
                  );
                }),
            Text(
              S.current.account_if_not_user_reference,
              style: vpTextStyle.body14.copyColor(vpColor.gray500),
            ),
            const SizedBox(height: 24),

            /// loading view
            buildLoadingView(),
          ],
        ),
      ),
    );
  }

  Widget buildLoadingView() {
    return BlocSelector<RegisterReferenceCubit, RegisterReferenceState, bool>(
      bloc: cubit,
      selector: (state) => state.loading,
      builder: (_, bool loading) {
        return loading
            ? const Center(child: VPBankLoading())
            : const SizedBox(height: 0);
      },
    );
  }

  @override
  void dispose() {
    pinCodeController.dispose();

    cubit.close();

    super.dispose();
  }
}
