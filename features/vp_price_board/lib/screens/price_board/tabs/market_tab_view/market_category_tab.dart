import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_price_board/widget/sort/normal_sort_view.dart';
import 'package:vp_price_board/widget/stock_list_cache_item.dart';
import 'package:vp_price_board/widget/stock_list_item.dart';
import 'package:vp_price_board/widget/stock_shimmer_list_item.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

import 'market_category_bloc.dart';

class MarketCategoryTabView extends StatefulWidget {
  const MarketCategoryTabView({required this.marketCode, super.key});

  final MarketCode marketCode;

  @override
  State<MarketCategoryTabView> createState() => _MarketCategoryTabViewState();
}

class _MarketCategoryTabViewState extends State<MarketCategoryTabView>
    with AutomaticKeepAliveClientMixin {
  ScrollController controller = ScrollController();

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return MultiBlocProvider(
      providers: [
        BlocProvider<MarketCategoryBloc>(
          create:
              (_) =>
                  MarketCategoryBloc(marketCode: widget.marketCode)..loadData(),
        ),
      ],

      child: BlocBuilder<MarketCategoryBloc, MarketCategoryState>(
        builder: (context, state) {
          if (state.apiStatus.isError) {
            return  ErrorWithRetryView( onRetry: () => onRefresh(context));
          }

          if (state.apiStatus.isLoading) {
            return const StockShimmerListItem();
          }

          if (state.apiStatus.isDone) {
            if (state.items.hasData) {
              return NormalSortView(
                onChanged: onSort,
                sortType: state.sortType,
                status: state.sortStatus,
                builder: (context, columns) {
                  return StockListView(
                    items: state.items!,
                    columns: columns,
                    controller: controller,
                    onRefresh: () => onRefresh(context),
                  );
                },
              );
            }

            if (state.stockCached != null) {
              return NormalSortView(
                onChanged: onSort,
                sortType: state.sortType,
                status: state.sortStatus,
                builder: (context, columns) {
                  return StockListCachedItem(
                    items: state.stockCached!,
                    columns: columns,
                  );
                },
              );
            }
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }

  Future onRefresh(BuildContext context) =>
      context.read<MarketCategoryBloc>().onRefresh();

  void onSort(BuildContext context, SortMode mode) {
    context.read<MarketCategoryBloc>().sortBy(mode);

    controller.jumpTo(0);
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;
}
