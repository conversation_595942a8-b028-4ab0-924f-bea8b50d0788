import 'package:app/common/assets/common_key_assets.dart';
import 'package:app/packages/shared/modules/account/data/model/register/account_laundering_money_item_question_model.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/money_laundering_prevention/list_value.dart';
import 'package:app/packages/stock/modules/place_order/presentation/feature/place_order/bloc/place_order_bloc_map_import.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SelectionValueWidget extends StatelessWidget {
  const SelectionValueWidget({
    Key? key,
    this.title,
    this.value,
    this.onTap,
  }) : super(key: key);
  final String? title;
  final MoneyLaunderingItemQuestionModel? value;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Visibility(
          visible: title != null,
          child: Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              title ?? '',
              style: TextStyleUtils.text16Weight500,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(SizeUtils.kRadius8),
            border: Border.all(color: ColorUtils.gray100),
            color: ColorUtils.bgInput,
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          child: InkWell(
            onTap: onTap,
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    value?.text ?? '',
                    style: vpTextStyle.body14?,
                  ),
                ),
                SvgPicture.asset(
                  CommonKeyAssets.icArrowDown,
                  width: Icon24,
                  colorFilter: ColorFilter.mode(
                    ColorUtils.gray900,
                    BlendMode.srcIn,
                  ),
                ),
              ],
            ),
          ),
        )
      ],
    );
  }
}
