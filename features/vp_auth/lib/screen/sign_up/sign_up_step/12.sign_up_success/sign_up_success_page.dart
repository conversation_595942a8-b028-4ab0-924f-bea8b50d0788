import 'dart:io';

import 'package:app/common/utils/app_log_utils.dart';
import 'package:app/common/utils/app_time_utils.dart';
import 'package:app/packages/shared/modules/account/common/assets/account_key_assets.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/data/model/register/account_open_vpbank_neo.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/view/components/sign_up_button.dart';
import 'package:core_ui/utils/color_utils.dart';
import 'package:core_ui/utils/size_utils.dart';
import 'package:core_ui/utils/text_utils.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class SignUpSuccessPage extends StatelessWidget {
  const SignUpSuccessPage({
    Key? key,
    this.signUpData,
  }) : super(key: key);

  final SignUpData? signUpData;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorUtils.white,
      body: SafeArea(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child:
                  Column(mainAxisAlignment: MainAxisAlignment.start, children: [
                const SizedBox(height: 40),
                SvgPicture.asset(
                  AccountKeyAssets.icSuccess,
                  color: ColorUtils.primary,
                  height: 88,
                  width: 88,
                ),
                const SizedBox(height: 24),
                Text(
                  getAccountLang(SignUpData().isComboWithBank
                      ? AccountKeyLang.registerSuccessCombo
                      : AccountKeyLang.registerSuccess),
                  style: TextStyleUtils.text20Weight700
                      .copyWith(color: ColorUtils.primary),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                SignUpData().agreeOpenBankAccount
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                              getAccountLang(
                                  AccountKeyLang.contentSuccessCombo1),
                              style: TextStyleUtils.text16Weight400.copyWith(
                                  color: ColorUtils.gray700, height: 1.6),
                              textAlign: TextAlign.center),
                          const SizedBox(height: 30),
                          Text(
                              getAccountLang(
                                  AccountKeyLang.contentSuccessCombo2),
                              style: TextStyleUtils.text16Weight400.copyWith(
                                  color: ColorUtils.gray700, height: 1.6),
                              textAlign: TextAlign.center)
                        ],
                      )
                    : Text(getAccountLang(AccountKeyLang.contentSuccess),
                        style: TextStyleUtils.text16Weight400
                            .copyWith(color: ColorUtils.gray700, height: 1.6),
                        textAlign: TextAlign.center),
                // const SizedBox(height: 16),
                // buildStackVPBankNeo(context)
              ]),
            ),
            const Spacer(),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                  border: Border(
                      top: BorderSide(
                color: ColorUtils.divider,
                width: 1,
              ))),
              child: SignUpButton(
                color: ColorUtils.primary,
                press: () => onFinish(context),
                text: getAccountLang(AccountKeyLang.startInvestVPBank),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void onFinish(BuildContext context) {
    EasyDebounce.debounce('onFinish', const Duration(milliseconds: 300), () {
      Navigator.pop(context);
    });
  }

  // Build Stack view Open SDK VPBank
  Stack buildStackVPBankNeo(BuildContext context) {
    final r0 = '${getAccountLang(AccountKeyLang.sdkVpR0)} ';
    final r1 = '\n\n• ${getAccountLang(AccountKeyLang.sdkVpR1)}';
    final r2 = '\n• ${getAccountLang(AccountKeyLang.sdkVpR2)}';
    final r3 = '\n• ${getAccountLang(AccountKeyLang.sdkVpR3)}';
    final r4 = '\n• ${getAccountLang(AccountKeyLang.sdkVpR4)}';
    return Stack(
      alignment: Alignment.center,
      children: [
        SvgPicture.asset(AccountKeyAssets.icLogoOpacity, height: 100),
        Padding(
          padding: const EdgeInsets.all(16),
          child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              width: double.maxFinite,
              decoration: BoxDecoration(
                  color: ColorUtils.primary.withOpacity(0.16),
                  borderRadius: BorderRadius.circular(10)),
              child: Column(
                children: [
                  const SizedBox(height: 16),
                  Text(getAccountLang(AccountKeyLang.sdkVPTitle),
                      style: TextStyleUtils.text14Weight600),
                  const SizedBox(height: 16),
                  Text('$r0$r1$r2$r3$r4',
                      style: vpTextStyle.body14?
                          .copyWith(color: ColorUtils.gray700, height: 2),
                      textAlign: TextAlign.start),
                  const SizedBox(height: 16),
                  TextButton(
                      onPressed: () {
                        openAccVPbankNeo(context);
                      },
                      child: Text(
                        getAccountLang(AccountKeyLang.sdkAction),
                        style: TextStyleUtils.text16Weight600
                            .copyWith(color: ColorUtils.primary),
                      ))
                ],
              )),
        )
      ],
    );
  }

  /// ### Open SDK VPBankNeo
  void openAccVPbankNeo(BuildContext context) async {
    String deviceName = 'Iphone 12';
    try {
      final deviceInfo = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        deviceName = androidInfo.model ?? deviceName;
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        deviceName = iosInfo.utsname.machine ?? deviceName;
      }
    } catch (e) {
      dlog(e);
    }

    // Du lieu OCR
    AccountOpenVPbankNeo objVpBankNeo = AccountOpenVPbankNeo();
    objVpBankNeo.mobileNumber = signUpData?.mobilePhone;
    objVpBankNeo.email = signUpData?.email;
    objVpBankNeo.pid = signUpData?.ocrData.idcode;
    objVpBankNeo.fullName = signUpData?.ocrData.fullname;
    objVpBankNeo.dob = signUpData?.ocrData.dateofbirth;
    objVpBankNeo.pidIssuedDate = signUpData?.ocrData.idDate;
    objVpBankNeo.pidIssuedPlace = 'Cuc canh sat';
    objVpBankNeo.pidExpiredDate = signUpData?.ocrData.expriedDate;
    objVpBankNeo.vpsEkyc = 'true';
    objVpBankNeo.registeredSmoDate = AppTimeUtils.formatTime(
        DateTime.now().toString(),
        format: AppTimeUtilsFormat.dateNormal);
    dlog(objVpBankNeo.getJsonString());
  }
}
