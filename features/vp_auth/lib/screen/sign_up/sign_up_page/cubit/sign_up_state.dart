import 'package:flutter/material.dart';

@immutable
abstract class SignUpState {}

class SignUpStateInitial extends SignUpState {}

class SignUpStateTitle extends SignUpState {
  final String title;
  final String? subTitle;

  SignUpStateTitle({
    required this.title,
    this.subTitle,
  });
}

class SignUpStateEnableButton extends SignUpState {
  final bool enable;
  final String? title;

  SignUpStateEnableButton({
    required this.enable,
    this.title,
  });
}

class SignUpStateProcess extends SignUpState {
  final int process;

  SignUpStateProcess({required this.process});
}
