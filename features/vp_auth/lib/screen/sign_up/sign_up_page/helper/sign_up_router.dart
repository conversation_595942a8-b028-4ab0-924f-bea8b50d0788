// import 'package:vp_common/utils/app_keyboard_utils.dart';
import 'package:flutter/material.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/sign_up_flow.dart';
import 'package:vp_auth/screen/sign_up/sign_up_step/10.sign_up_acc_number/sign_up_acc_number_page.dart';
import 'package:vp_auth/screen/sign_up/sign_up_step/11.sign_up_contract/sign_up_contract_page.dart';
import 'package:vp_auth/screen/sign_up/sign_up_step/2.sign_up_otp/sign_up_otp_page.dart';
import 'package:vp_auth/screen/sign_up/sign_up_step/3.sign_up_email/sign_up_email_page.dart';
import 'package:vp_auth/screen/sign_up/sign_up_step/4.1sign_up_nfc/sign_up_scan_nfc_page.dart';
import 'package:vp_auth/screen/sign_up/sign_up_step/4.sign_up_supporter/sign_up_supporter_page.dart';
import 'package:vp_auth/screen/sign_up/sign_up_step/5.1sign_up_signature/sign_up_signature_page.dart';
import 'package:vp_auth/screen/sign_up/sign_up_step/6.sign_up_info_ocr/sign_up_info_ocr_page.dart';
import 'package:vp_auth/screen/sign_up/sign_up_step/7.sign_up_info_edit/sign_up_info_edit_page.dart';
import 'package:vp_auth/screen/sign_up/sign_up_step/8.sign_up_ekyc/sign_up_ekyc_page.dart';
import 'package:vp_auth/screen/sign_up/sign_up_step/9.sign_up_bank/sign_up_bank_page.dart';
import 'package:vp_auth/screen/sign_up/sign_up_step/money_laundering_prevention/money_laundering_prevention_page.dart';
import 'package:vp_auth/screen/sign_up/sign_up_step/sign_up_referrer/sign_up_referrer_page.dart';
import 'package:vp_common/utils/app_routes_utils/animate_routes.dart';

import '../../sign_up_step/1.sign_up_phone/sign_up_phone.dart';
import '../../sign_up_step/5.sign_up_upload/sign_up_upload_page.dart';

class SignUpRouter {
  static final SignUpRouter _singleton = SignUpRouter._internal();

  factory SignUpRouter() => _singleton;

  SignUpRouter._internal();

  GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  List<SignUpTypeScreen> _listScreen = [];

  SignUpTypeScreen get currentScreen => _listScreen.last;

  init() {
    // navigatorKey = GlobalKey<NavigatorState>();
    _listScreen = [SignUpTypeScreen.phone];
  }

  Route onGenerateRoute(RouteSettings settings) {
    late Widget page = const SizedBox();
    if (settings.name == SignUpTypeScreen.phone.name) {
      page = const SignUpPhonePage();
    } else if (settings.name == SignUpTypeScreen.otp.name) {
      page = SignUpOtpPage(sessionID: settings.arguments as String?);
    } else if (settings.name == SignUpTypeScreen.email.name) {
      page = const SignUpEmailPage();
    } else if (settings.name == SignUpTypeScreen.supporter.name) {
      page = const SignUpSupporterPage();
    } else if (settings.name == SignUpTypeScreen.upload.name) {
      page = const SignUpUploadPage();
    } else if (settings.name == SignUpTypeScreen.nfc.name) {
      page = const SignUpScanNfcPage();
    } else if (settings.name == SignUpTypeScreen.signature.name) {
      page = const SignUpSignaturePage();
    } else if (settings.name == SignUpTypeScreen.infoOcr.name) {
      page = const SignUpInfoOcrPage();
    } else if (settings.name == SignUpTypeScreen.editInfoOcr.name) {
      page = const SignUpInfoEditPage();
    } else if (settings.name == SignUpTypeScreen.ekyc.name) {
      page = const SignUpEkycPage();
    } else if (settings.name == SignUpTypeScreen.bank.name) {
      page = const SignUpBankPage();
    } else if (settings.name == SignUpTypeScreen.accNumber.name) {
      page = const SignUpAccNumberPage();
    } else if (settings.name == SignUpTypeScreen.contract.name) {
      page = const SignUpContractPage();
    } else if (settings.name == SignUpTypeScreen.referrer.name) {
      page = const RegisterReferencePage();
    } else if (settings.name == SignUpTypeScreen.moneyLaundering.name) {
      page = const MoneyLaunderingPreventionPage();
    }
    return SlideRightRoute(page: page, settings: settings);
  }

  void onPushReplace(SignUpTypeScreen type) {
    // AppKeyboardUtils.unFocusTextField();
    _listScreen.removeLast();
    _listScreen.add(type);
    navigatorKey.currentState?.pushReplacementNamed(type.name);
  }

  void onPush(SignUpTypeScreen type, {dynamic value}) {
    if (type != currentScreen) {
      // AppKeyboardUtils.unFocusTextField();
      _listScreen.add(type);
      navigatorKey.currentState?.pushNamed(type.name, arguments: value);
    }
  }

  void onBack() {
    if (navigatorKey.currentState?.canPop() ?? false) {
      // AppKeyboardUtils.unFocusTextField();
      _listScreen.removeLast();
      navigatorKey.currentState?.pop();
      Future.delayed(const Duration(milliseconds: 200))
          .then((value) => SignUpFlow().onResume());
    }
  }

  void onBackUtil(SignUpTypeScreen type) {
    final canPop = navigatorKey.currentState?.canPop() ?? false;

    if (canPop) {
      // AppKeyboardUtils.unFocusTextField();
      while (_listScreen.isNotEmpty) {
        _listScreen.removeLast();
        navigatorKey.currentState?.pop();
        if (_listScreen.last == type) break;
      }
      Future.delayed(const Duration(milliseconds: 200))
          .then((value) => SignUpFlow().onResume());
    }
  }

  onClear() {
    _listScreen.clear();
    // navigatorKey.currentState?.dispose(); // NavigatorState doesn't have dispose method
  }
}

enum SignUpTypeScreen {
  init,
  phone,
  otp,
  email,
  supporter,
  upload,
  nfc,
  signature,
  infoOcr,
  editInfoOcr,
  ekyc,
  bank,
  accNumber,
  contract,
  referrer,
  moneyLaundering,
}
