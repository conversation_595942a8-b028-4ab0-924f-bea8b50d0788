
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_auth/generated/l10n.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/cubit/sign_up_bloc.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/cubit/sign_up_state.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/sign_up_tracking.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/view/components/sign_up_button.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class SignUpBottomView extends StatelessWidget {
  const SignUpBottomView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
        child: Column(
            children: [_SignUpBottomProcessView(), _SignUpBottomActionView()]));
  }
}

class _SignUpBottomProcessView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    const height = 3.0;
    return Column(
      children: [
        const SizedBox(height: 4),
        Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(3),
                color: themeData.gray100,
              ),
              height: height,
              width: double.maxFinite,
            ),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(3),
                color: themeData.primary,
              ),
              height: height,
              width: MediaQuery.of(context).size.width * 0.02,
            ),
            BlocBuilder<SignUpBloc, SignUpState>(
                buildWhen: ((previous, current) =>
                    current is SignUpStateProcess),
                builder: (context, state) {
                  double widthProcess = 0;
                  if (state is SignUpStateProcess) {
                    widthProcess = (MediaQuery.of(context).size.width / 100) *
                        state.process;
                  }
                  return AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(3),
                      color: themeData.primary,
                    ),
                    height: height,
                    width: widthProcess,
                  );
                }),
          ],
        ),
        const SizedBox(height: 16)
      ],
    );
  }
}

class _SignUpBottomActionView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Padding(
        padding: const EdgeInsets.only(bottom: 16, right: 16, left: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                AccountButtonBackDefault(
                  title: S.current.account_back,
                  onPressed: () async {
                    NoDuplicate(() {
                      if (SignUpRouter().currentScreen ==
                          SignUpTypeScreen.phone) {
                        Navigator.pop(context);
                        return;
                      }
                      SignUpTracking().signupBack();
                      SignUpRouter().onBack();
                    });
                  },
                ),
                const Expanded(child: SizedBox()),
                BlocBuilder<SignUpBloc, SignUpState>(
                  buildWhen: (previous, current) =>
                      current is SignUpStateEnableButton,
                  builder: (context, state) {
                    String title = state is SignUpStateEnableButton
                        ? (state.title ?? '')
                        : '';
                    return SignUpButtonNext(
                        enable: (state is SignUpStateEnableButton)
                            ? state.enable
                            : false,
                        onPress: () {
                          context.read<SignUpBloc>().emitOnNext();
                        },
                        title: title.isNotEmpty
                            ? title
                            : VPCommonLocalize.current.next);
                  },
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}
