import 'dart:async';

import 'package:app/packages/bond_service/common/constains.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/5.sign_up_upload/components/sign_up_upload_guide_view.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/fpt_ocr_ekyc/fpt_ocr/sign_up_base_stream.dart';
import 'package:camera/camera.dart';
import 'package:core_ui/utils/color_utils.dart';
import 'package:core_ui/utils/size_utils.dart';
import 'package:core_ui/utils/text_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../../../common/assets/account_key_assets.dart';
import '../fpt_helper/fpt_loading.dart';
import 'account_camera_ocr_bloc.dart';
import 'components/account_err_ocr_dialog.dart';

class AccountCameraOcr extends StatefulWidget {
  const AccountCameraOcr(
      {Key? key, required this.camera, required this.currentType})
      : super(key: key);

  final CameraDescription camera;
  final AccountTypeIDCard currentType;

  @override
  State<AccountCameraOcr> createState() => AccountCameraOcrState();
}

class AccountCameraOcrState extends State<AccountCameraOcr> {
  late CameraController controller;
  late Future<void> initiallizeControllerFuture;
  AccountCameraOcrBloc bloc = AccountCameraOcrBloc();
  late StreamSubscription subscription;

  @override
  void initState() {
    super.initState();

    controller = CameraController(
      widget.camera,
      ResolutionPreset.high,
      enableAudio: false,
    );

    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

    initiallizeControllerFuture = controller.initialize()
      ..then(
        (_) {
          controller.lockCaptureOrientation(DeviceOrientation.portraitUp);
        },
      );
    subscription = bloc.streamData.listen((obj) {
      if (obj is SignUpBaseStreamObj<String>) {
        if (obj.message.isEmpty) {
          Navigator.pop(context, obj.data);
        } else {
          final message = widget.currentType == AccountTypeIDCard.front
              ? getAccountLang(AccountKeyLang.wrongFrontIDCard)
              : getAccountLang(AccountKeyLang.wrongBackIDCard);
          _showMyDialog(message);
        }
      }
    });
  }

  @override
  void dispose() {
    controller.dispose();
    subscription.cancel();
    bloc.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
          backgroundColor: Colors.black87,
          appBar: buildAppBar(context),
          body: buildCameraView()),
    );
  }

  /*-------- Build AppBar -------- */
  AppBar buildAppBar(BuildContext context) {
    return AppBar(
      title: Text(
          getAccountLang(widget.currentType == AccountTypeIDCard.front
              ? AccountKeyLang.fontIdentiCard
              : AccountKeyLang.backIdentiCard),
          style: TextStyleUtils.text18Weight700
              .copyWith(color: ColorDefine.white)),
      centerTitle: true,
      backgroundColor: Colors.black,
      leading: const SizedBox(),
    );
  }

  /*-------- Build CameraView -------- */
  FutureBuilder<void> buildCameraView() {
    final size = MediaQuery.of(context).size;
    return FutureBuilder<void>(
      future: initiallizeControllerFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          final cameraPreview = CameraPreview(controller);

          return Stack(
            children: [
              CustomPaint(
                foregroundPainter: _OCRPaint(),
                child: SizedBox(
                    width: controller.value.previewSize?.width,
                    height: controller.value.previewSize?.height,
                    child: cameraPreview),
              ),
              SizedBox(
                width: size.width,
                height: double.infinity,
                child: CustomPaint(
                  foregroundPainter: _OCRCustomPaint(),
                  child:
                      ClipPath(clipper: _OCRClipRect(), child: cameraPreview),
                ),
              ),
              buildBottomCamera(),
              buildLoading()
            ],
          );
        } else {
          return const Center(child: CircularProgressIndicator());
        }
      },
    );
  }

  /*-------- Build Bottom View -------- */
  Align buildBottomCamera() {
    return Align(
        alignment: Alignment.bottomCenter,
        child: Container(
          width: double.infinity,
          height: 200,
          color: Colors.black,
          child: Column(
            children: [
              kSpacingHeight12,
              TextButton(
                  onPressed: () {
                    showBottomSheetGuideOcr(
                        index: widget.currentType == AccountTypeIDCard.front
                            ? 0
                            : 1);
                  },
                  child: Text('Hướng dẫn chụp ảnh',
                      style: TextStyleUtils.text16Weight500
                          .copyWith(color: ColorUtils.primary))),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                    'Đặt giấy tờ lên mặt phẳng, giữ giấy tờ nằm trong khung hình và đảm bảo thông tin hiển thị rõ ràng.',
                    style: vpTextStyle.body14?
                        .copyWith(color: ColorDefine.white),
                    textAlign: TextAlign.center),
              ),
              Expanded(
                child: Stack(
                  children: [
                    buildButtonCapture(),
                    Align(
                        alignment: Alignment.centerLeft,
                        child: TextButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            child: Text(
                              getAccountLang(AccountKeyLang.cancel),
                              style: TextStyleUtils.text18Weight400
                                  .copyWith(color: ColorDefine.white),
                            ))),
                  ],
                ),
              ),
            ],
          ),
        ));
  }

  /*-------- Build button chụp ảnh -------- */
  Align buildButtonCapture() {
    return Align(
      alignment: Alignment.center,
      child: InkWell(
        onTap: () {
          captureImage();
        },
        child: SvgPicture.asset(AccountKeyAssets.btnCapture),
      ),
    );
  }

  /*-------- Build ViewLoadingAndDialog -------- */
  StreamBuilder buildLoading() {
    return StreamBuilder<AccountCameraOcrBlocState>(
        stream: bloc.streamStateUI,
        builder: (context, snapshot) {
          final currentState =
              snapshot.data ?? AccountCameraOcrBlocState.hideLoading;
          return [AccountCameraOcrBlocState.hideLoading].contains(currentState)
              ? const SizedBox.shrink()
              : Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  color: Colors.black.withOpacity(0.7),
                  child: buidLoadingOcr(),
                );
        });
  }

  /*-------- Build Loading API -------- */
  Center buidLoadingOcr() {
    return const Center(child: FptLoadingOcrEkyc());
  }

  /*-------- Xử lý chụp ảnh -------- */
  Future<void> captureImage() async {
    try {
      bloc.sinkState(AccountCameraOcrBlocState.loading);
      await initiallizeControllerFuture;
      await controller.setFlashMode(FlashMode.off);
      final imageXfile = await controller.takePicture();
      await bloc.saveImageIDCard(widget.currentType, imageXfile.path);
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      bloc.emitError(e.toString());
    } finally {
      bloc.sinkState(AccountCameraOcrBlocState.hideLoading);
    }
  }

  /*--------  Build dialog khi lỗi OCR -------- */
  Future<void> _showMyDialog(String messageErr) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
            backgroundColor: ColorUtils.bgPopup,
            content: AccountErrOcrDialog(messageErr: messageErr));
      },
    );
  }
}

/* ------------------ PAIN ------------------ */
class _OCRPaint extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    canvas.drawColor(Colors.grey.withOpacity(0.8), BlendMode.dstOut);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return true;
  }
}

double leftCamera = 25;

double heightY = 300;

double getLeft() {
  return leftCamera;
}

double getRight(double sizeWidth) {
  return sizeWidth - leftCamera;
}

double getTop(double sizeHeight) {
  return sizeHeight / 2 - heightY * 0.75;
}

double getBottom(double sizeHeight) {
  return getTop(sizeHeight) + heightY;
}

class _OCRClipRect extends CustomClipper<Path> {
  @override
  getClip(Size size) {
    Path path = Path()
      ..addRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTRB(
            getLeft(),
            getTop(size.height),
            getRight(size.width),
            getBottom(size.height),
          ),
          const Radius.circular(12),
        ),
      );
    return path;
  }

  @override
  bool shouldReclip(oldClipper) {
    return true;
  }
}

class _OCRCustomPaint extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    const padding = 15.0;

    const lineWidth = 50;

    final top = getTop(size.height);

    final rightCorner = getRight(size.width) - padding;

    final leftCorner = leftCamera + padding;

    final topCorner = top + padding;

    final bottomCorner = top + heightY - padding;

    final paint = Paint();

    paint.strokeWidth = 5;
    paint.color = Colors.white;
    paint.strokeCap = StrokeCap.round;

    /// draw left corner
    canvas.drawLine(
      Offset(leftCorner, topCorner),
      Offset(leftCorner + lineWidth, topCorner),
      paint,
    );

    canvas.drawLine(
      Offset(leftCorner, topCorner),
      Offset(leftCorner, topCorner + lineWidth),
      paint,
    );

    /// draw bottom corner
    canvas.drawLine(
      Offset(leftCorner, bottomCorner),
      Offset(leftCorner + lineWidth, bottomCorner),
      paint,
    );

    canvas.drawLine(
      Offset(leftCorner, bottomCorner),
      Offset(leftCorner, bottomCorner - lineWidth),
      paint,
    );

    /// draw top right corner
    canvas.drawLine(
      Offset(rightCorner, topCorner),
      Offset(rightCorner - lineWidth, topCorner),
      paint,
    );

    canvas.drawLine(
      Offset(rightCorner, topCorner),
      Offset(rightCorner, topCorner + lineWidth),
      paint,
    );

    /// draw bomttom right corner
    canvas.drawLine(
      Offset(rightCorner, bottomCorner),
      Offset(rightCorner - lineWidth, bottomCorner),
      paint,
    );

    canvas.drawLine(
      Offset(rightCorner, bottomCorner),
      Offset(rightCorner, bottomCorner - lineWidth),
      paint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}
