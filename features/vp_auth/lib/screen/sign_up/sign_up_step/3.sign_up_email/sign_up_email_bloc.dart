import 'dart:async';

import 'package:app/common/error/show_error.dart';
import 'package:app/common/utils/app_config_utils.dart';
import 'package:app/common/utils/app_validate.dart';
import 'package:app/global/session/app_session.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/onboarding_repository.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/cubit/sign_up_step.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_tracking.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/i_sign_up_step.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/sign_up_const.dart';
import 'package:core/di/injector.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

part 'sign_up_emai_state.dart';

class SignUpEmailBloc extends ISignUpController<SignUpEmailState> {
  SignUpEmailBloc() : super(const SignUpEmailState());

  @override
  String? get title => getAccountLang(AccountKeyLang.infoEmail);

  @override
  int? get progress => SignUpStep.email.process;

  Timer? _debounce;

  @override
  void init() {
    listenerTxtEmail();
  }

  @override
  bool isTypeScreen(SignUpTypeScreen value) {
    return value == SignUpTypeScreen.email;
  }

  void _loading(bool value) {
    emit(state.copyWith(isLoading: value, isReadOnly: value));
  }

  @override
  void onNext(SignUpTypeScreen type) {
    if (!state.isLoading) {
      apiCheckEmail();
    }
  }

  TextEditingController txtEmail = TextEditingController();

  void listenerTxtEmail() {
    txtEmail.addListener((() {
      if (_debounce?.isActive ?? false) {
        _debounce!.cancel();
      }
      setEnable(false);
      emit(state.copyWith(isShowGuide: false));
      _debounce =
          Timer(const Duration(milliseconds: AppConfigUtils.debounce), () {
        setEnable(false);
        emit(state.copyWith(isShowGuide: txtEmail.text.isEmpty));
        final email = txtEmail.text.removeAllWhitespace();
        if (email.isEmpty) {
          return;
        }
        valid(email)
            ? setEnable(true)
            : emit(state.copyWith(
                msgTextField:
                    getAccountLang(AccountKeyLang.errorInvalidEmail)));
      });
    }));
  }

  Future apiCheckEmail() async {
    _loading(true);
    final email = txtEmail.text.removeAllWhitespace();
    try {
      final value =
          await sl<OnboardingRepository>().onboardingUsersEmail(email);
      _loading(false);
      if (value != null) {
        if (value.isSuccess()) {
          SignUpTracking().signupEmailSuccess();

          SignUpData().setEmail(email);

          if (SignUpData().afChanel == SignUpConst.afChanelRbBank) {
            SignUpRouter().onPush(SignUpTypeScreen.referrer);
          } else {
            SignUpRouter().onPush(SignUpTypeScreen.supporter);
          }
        } else {
          emit(state.copyWith(msgTextField: value.message ?? ''));
        }
      }
    } catch (e) {
      _loading(false);
      SignUpTracking().signupEmailFail(e);
      showError(e);
    }
  }

  bool valid(String value) {
    return AppValidator.validEmail(value.trim());
  }

  @override
  Future<void> close() {
    txtEmail.dispose();
    if (_debounce?.isActive ?? false) {
      _debounce!.cancel();
    }
    return super.close();
  }
}
