import 'package:app/common/assets/common_key_assets.dart';
import 'package:app/packages/bond_service/common/constains.dart';
import 'package:core_ui/utils/color_utils.dart';
import 'package:core_ui/utils/size_utils.dart';
import 'package:core_ui/utils/text_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SupportItemSelectView extends StatelessWidget {
  final bool isSelected;
  final String title;
  final Function onCallBack;

  const SupportItemSelectView(
      {Key? key,
      required this.title,
      required this.isSelected,
      required this.onCallBack})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onCallBack();
      },
      child: Container(
        decoration: BoxDecoration(
            border: Border.all(color: ColorUtils.gray300),
            borderRadius: BorderRadius.circular(10)),
        height: 56,
        child: Row(children: [
          kSpacingWidth16,
          SvgPicture.asset(isSelected
              ? CommonKeyAssets.radio2
              : CommonKeyAssets.icCheckBoxNone),
          kSpacingWidth8,
          Expanded(
              child: Text(title,
                  style: vpTextStyle.body14?
                      .copyWith(color: ColorUtils.black))),
          kSpacingWidth8,
        ]),
      ),
    );
  }
}
