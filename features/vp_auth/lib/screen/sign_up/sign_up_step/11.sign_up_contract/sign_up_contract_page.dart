import 'dart:async';

import 'package:app/common/widgets/divider_widget.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/11.sign_up_contract/components/sign_up_contract_item.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/11.sign_up_contract/sign_up_contract_cubit.dart';
import 'package:app/packages/shared/modules/money/money_presentation/feature/money_cash_in/view/components/vpbank_loading_money.dart';
import 'package:core_ui/core_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SignUpContractPage extends StatelessWidget {
  const SignUpContractPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
        create: ((_) => SignUpContractCubit()),
        child: _BodySignUpContractPage());
  }
}

class _BodySignUpContractPage extends StatefulWidget {
  @override
  State<_BodySignUpContractPage> createState() =>
      _BodySignUpContractPageState();
}

class _BodySignUpContractPageState extends State<_BodySignUpContractPage> {
  late SignUpContractCubit bloc;

  late StreamSubscription _subscription;

  /// Kiem tra xem co duoc reload hop dong hay khong
  bool canReloadListContract = true;

  @override
  void initState() {
    super.initState();

    bloc = context.read<SignUpContractCubit>();

    callApiGenerationContract();

    _subscription = bloc.controllerShowFile.stream.listen((value) {
      if (value && mounted) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: ColorUtils.bgMain,
      child: Column(
        children: [
          Expanded(
              child: Column(
            children: [
              _Loading(),
              Expanded(
                child: Column(
                  children: buildListContract(),
                ),
              ),
            ],
          )),
          DividerWidget(),
          buildRichTextNoteContract()
        ],
      ),
    );
  }

  List<Widget> buildListContract() {
    return List.generate(bloc.listContract.length, (index) {
      return SignUpContractItem(
        obj: bloc.listContract[index],
        callBack: () {
          final isOpened = bloc.listContract[index].isSelected;

          if (!canReloadListContract) {
            return;
          }
          setState(() {
            bloc.reloadListContract(index);
          });
          if (!isOpened) {
            canReloadListContract = false;
          }
        },
        download: () {
          bloc.downloadContractFromAssets(index);
        },
        statusLoad: (value) {
          canReloadListContract = true;
        },
      );
    });
  }

  /* -------- RickhText VPBank --------*/
  Widget buildRichTextNoteContract() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: RichText(
        text: TextSpan(
          text: getAccountLang(AccountKeyLang.contractNote1),
          style: vpTextStyle.body14?
              .copyWith(color: ColorUtils.gray500),
          children: [
            TextSpan(
                text: ' ${getAccountLang(AccountKeyLang.contractNote2)}',
                style: TextStyleUtils.text14Weight600
                    .copyWith(color: ColorUtils.black)),
            TextSpan(
                text: getAccountLang(AccountKeyLang.contractNote3),
                style: vpTextStyle.body14?
                    .copyWith(color: ColorUtils.gray500)),
          ],
        ),
      ),
    );
  }

  // Call API generate contract
  void callApiGenerationContract() {
    canReloadListContract = true;
    bloc.setEnable(false);
    bloc.apiGenerationContract();
  }
}

class _Loading extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SignUpContractCubit, SignUpContractState>(
      buildWhen: (previous, current) => previous.isLoading != current.isLoading,
      builder: (context, state) {
        return state.isLoading
            ? const Padding(
                padding: EdgeInsets.only(top: 80),
                child: VPBankLoading(),
              )
            : const SizedBox.shrink();
      },
    );
  }
}
