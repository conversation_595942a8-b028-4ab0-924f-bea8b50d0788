import 'package:flutter/material.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/screen/derivatives_order_book/regular_order_book/widget/derivatives_edit_order_bottom_sheet_wrapper.dart';

/// Shows the derivatives order edit dialog using the new Cubit-based architecture
void dialogConfirmEditDerivativesOrders(
  BuildContext context,
  OrderBookModel item, {
  required Function(int quantity, double price) onConfirm,
  VoidCallback? onCancel,
}) async {
  VPPopup.bottomSheet(
    DerivativesEditOrderBottomSheetWrapper(
      model: item,
      onEditSuccess: () {
        Navigator.of(context).pop();
        // Convert the new architecture callback to the legacy format
        // This is a temporary bridge until the calling code is updated
        onConfirm(item.qty ?? 0, double.tryParse(item.price ?? '0') ?? 0.0);
      },
      onEditFailure: (errorMessage) {
        Navigator.of(context).pop();
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(errorMessage), backgroundColor: Colors.red),
        );
        onCancel?.call();
      },
    ),
  ).showSheet(context);
}
