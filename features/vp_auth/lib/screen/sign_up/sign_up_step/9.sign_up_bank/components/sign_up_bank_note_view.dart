import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:flutter/material.dart';

class SignUpBankNoteView extends StatelessWidget {
  const SignUpBankNoteView({Key? key}) : super(key: key);
  final urlRegisterVPBank = 'https://taikhoan.vpbank.com.vn/';

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: '${getAccountLang(AccountKeyLang.note)}: ',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).disabledColor,
                    ),
              ),
              TextSpan(
                text: getAccountLang(AccountKeyLang.signUpBankNote),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w400,
                      color: Theme.of(context).disabledColor,
                    ),
              ),
            ],
          ),
        ),
        // Text(
        //   getAccountLang(AccountKeyLang.signUpBankNote2),
        //   style: Theme.of(context).textTheme.titleMedium?.copyWith(
        //         fontWeight: FontWeight.w400,
        //         color: Theme.of(context).disabledColor,
        //       ),
        // ),
      ],
    );
  }
}
