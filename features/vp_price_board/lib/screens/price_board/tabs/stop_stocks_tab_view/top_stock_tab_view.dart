import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_price_board/core/repository/price_board_repository.dart';
import 'package:vp_price_board/model/enum/top_stocks_type.dart';
import 'package:vp_price_board/widget/sort/normal_sort_view.dart';
import 'package:vp_price_board/widget/stock_list_item.dart';
import 'package:vp_price_board/widget/stock_list_item_builder_view.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

import 'top_stocks_category_cubit.dart';

class TopStocksTabView extends StatefulWidget {
  const TopStocksTabView({super.key});

  @override
  State<TopStocksTabView> createState() => _TopStocksTabViewState();
}

class _TopStocksTabViewState extends State<TopStocksTabView>
    with AutomaticKeepAliveClientMixin {
  ScrollController controller = ScrollController();

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create:
              (context) => TopStocksCategoryBloc(
                repository: GetIt.instance.get<PriceBoardRepository>(),
              ),
        ),
      ],

      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 10, 16, 10),
            child: Row(
              children: [
                BlocBuilder<TopStocksCategoryBloc, TopStocksCategoryState>(
                  builder: (context, state) {
                    return VPDropdownView.small(
                      value: state.topStocksType.name,
                      onTap: () async => showBottomSheet(context),
                    );
                  },
                ),

                const SizedBox(width: 10),

                Expanded(
                  child: VPTabBarSelectorView<MarketCode>(
                    tabs: const [
                      MarketCode.HOSE,
                      MarketCode.HNX,
                      MarketCode.UPCOM,
                    ],
                    textBuilder: (e) => e.name,
                    onChanged:
                        (context, item) => context
                            .read<TopStocksCategoryBloc>()
                            .onExchangeChange(item),
                  ),
                ),
              ],
            ),
          ),

          Expanded(
            child: BlocBuilder<TopStocksCategoryBloc, TopStocksCategoryState>(
              builder: (context, state) {
                return StockListItemBuilderView(
                  apiStatus: state.apiStatus,
                  data: state.items,
                  onRetry: () => onRefresh(context),
                  builder: (context, data) {
                    return NormalSortView(
                      onChanged: onSort,
                      sortType: state.sortType,
                      status: state.sortStatus,
                      builder: (context, columns) {
                        return StockListView(
                          items: data,
                          columns: columns,
                          controller: controller,
                          onRefresh: () => onRefresh(context),
                        );
                      },
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Future onRefresh(BuildContext context) =>
      context.read<TopStocksCategoryBloc>().onRefresh();

  void showBottomSheet(BuildContext context) async {
    final result = await VPPopup.bottomAction(
          items:
              TopStocksType.values
                  .map(
                    (e) => ListTile(
                      onTap: () => Navigator.pop(context, e),
                      title: Text(
                        e.name,
                        textAlign: TextAlign.center,
                        style: vpTextStyle.subtitle16.copyColor(
                          vpColor.textPrimary,
                        ),
                      ),
                    ),
                  )
                  .toList(),
        )
        .copyWith(
          button: VPDynamicButton.primaryMedium(
            text: 'Huỷ',
            onTap: () => Navigator.pop(context),
            textColor: vpColor.textAccentRed,
            backgroundColor: vpColor.backgroundElevation0,
          ),
        )
        .showAction(context);

    if (!context.mounted) return;

    if (result is! TopStocksType) return;

    context.read<TopStocksCategoryBloc>().onTypeChange(result);
  }

  void onSort(BuildContext context, SortMode mode) {
    context.read<TopStocksCategoryBloc>().sortBy(mode);

    controller.jumpTo(0);
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;
}
