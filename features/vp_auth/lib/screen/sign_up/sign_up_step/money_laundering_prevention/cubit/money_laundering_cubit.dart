import 'dart:async';

import 'package:app/common/assets/common_key_assets.dart';
import 'package:app/common/constants/app_constants.dart';
import 'package:app/common/error/show_error.dart';
import 'package:app/common/utils/app_device_id.dart';
import 'package:app/common/utils/app_helper.dart';
import 'package:app/common/utils/app_launching_utils.dart';
import 'package:app/common/utils/app_log_utils.dart';
import 'package:app/common/utils/loading_utils.dart';
import 'package:app/global/navigator/app_router.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/data/model/register/account_laundering_money_item_question_model.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/onboarding_repository.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/cubit/sign_up_step.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/i_sign_up_step.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/money_laundering_prevention/cubit/money_laundering_state.dart';
import 'package:core/core.dart';
import 'package:core_ui/utils/color_utils.dart';
import 'package:core_ui/utils/text_utils.dart';
import 'package:core_ui/widgets/design_system/dialog/notifty_dialog.dart';
import 'package:flutter/material.dart';

class MoneyLaunderingCubit extends ISignUpController<MoneyLaunderingState> {
  MoneyLaunderingCubit() : super(MoneyLaunderingState());

  OnboardingRepository get repository => sl.get<OnboardingRepository>();

  // default value
  bool confirm = true;

  @override
  void onNext(SignUpTypeScreen type) async {
    if (isTypeScreen(type)) {
      if (!confirm) {
        showDialogWarning();
        return;
      }
      _onConfirm(true);
    }
  }

  void onInitData() async {
    try {
      final purposes = await sl
          .get<OnboardingRepository>()
          .getListQuestionLaunderingMoneyModel(QuestionCodeConst.purpose);
      emit(state.copyWith(loading: true));
      final jobs = await sl
          .get<OnboardingRepository>()
          .getListQuestionLaunderingMoneyModel(QuestionCodeConst.job);
      final positions = await sl
          .get<OnboardingRepository>()
          .getListQuestionLaunderingMoneyModel(QuestionCodeConst.position);
      final defaultPurpose =
          _findDefaultValue(QuestionCodeConst.idDefaultPurpose, purposes);
      final defaultJob =
          _findDefaultValue(QuestionCodeConst.idDefaultJob, jobs);
      final defaultPosition =
          _findDefaultValue(QuestionCodeConst.idDefaultPosition, positions);
      emit(state.copyWith(
        job: defaultJob,
        position: defaultPosition,
        purpose: defaultPurpose,
        listJob: jobs,
        listPosition: positions,
        listPurpose: purposes,
      ));
    } catch (e) {
      showError(e);
    }
  }

  void _onConfirm(bool confirm) async {
    try {
      LoadingUtil.showLoading();
      String? macAddress =
          await AppHelper().getMacAddress() ?? await AppUtils.getDeviceId();
      String valueConfirm = confirm ? 'N' : 'Y';
      final inputData = {
        "purposeAccount": state.purpose?.id ?? '',
        "job": state.job?.id ?? '',
        "jobPosition": state.position?.id ?? '',
        "trustee": valueConfirm,
        "beneficialOwner": valueConfirm,
        "fatca": valueConfirm,
        "macAddress": macAddress
      };

      SignUpData().agreeOpenBankAccount = false;
      var data = await sl
          .get<OnboardingRepository>()
          .onSubmitMoneyLaundering(inputData);
      // Luồng chạy ngầm => try catch để không block luồng
      try {
        if (data) {
          await sl.get<OnboardingRepository>().verifyNFC();
        }
      } catch (e) {
        dlog(e);
      }
      if (confirm) {
        SignUpData().agreeOpenBankAccount = true;
      }
      LoadingUtil.hideLoading();
      SignUpRouter().onPush(SignUpTypeScreen.accNumber);
    } catch (e) {
      LoadingUtil.hideLoading();
      showError(e);
    }
  }

  void showDialogWarning() {
    if (navigation.navigatorKey.currentContext == null) {
      return;
    }
    showNotifyDialog(
      context: navigation.navigatorKey.currentContext!,
      barrierDismissible: false,
      colorButtonRight: ColorUtils.primary,
      title: getAccountLang(AccountKeyLang.openAnAccount),
      titleStyle: TextStyleUtils.text20Weight700.copyWith(
        color: ColorUtils.gray700,
      ),
      iconSize: 60,
      image: CommonKeyAssets.icWarning,
      imagePadding: const EdgeInsets.only(bottom: 24),
      textButtonRight: getAccountLang(AccountKeyLang.understood),
      colorBorderButtonLeft: ColorUtils.gray700,
      contentWidget: InkWell(
        onTap: () {
          navigation.goBack();
          Timer(const Duration(milliseconds: 200), () {
            callNow(AppConstants.support);
          });
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 30),
          child: Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: getAccountLang(AccountKeyLang.noteOpenAnAccount1),
                  style: vpTextStyle.body14?.copyWith(
                    color: ColorUtils.gray700,
                  ),
                ),
                TextSpan(
                  text: AppConstants.support,
                  style: vpTextStyle.body14?.copyWith(
                    color: ColorUtils.primary,
                  ),
                ),
                TextSpan(
                  text: getAccountLang(AccountKeyLang.noteOpenAnAccount2),
                  style: vpTextStyle.body14?.copyWith(
                    color: ColorUtils.gray700,
                  ),
                ),
              ],
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
      onPressedRight: () {
        navigation.goBack();
        Timer(const Duration(milliseconds: 200), () {
          _onConfirm(false);
        });
      },
    );
  }

  @override
  bool isTypeScreen(SignUpTypeScreen value) {
    return value == SignUpTypeScreen.moneyLaundering;
  }

  @override
  String? get title => getAccountLang(AccountKeyLang.moneyLaunderingPrevention);

  @override
  int? get progress => SignUpStep.moneyLaundering.process;

  @override
  bool get enable => true;

  MoneyLaunderingItemQuestionModel? _findDefaultValue(
      String id, List<MoneyLaunderingItemQuestionModel> list) {
    for (final obj in list) {
      if (obj.id == id) {
        return obj;
      }
    }
    return null;
  }

  void onUpdatePurpose(MoneyLaunderingItemQuestionModel model) {
    emit(state.copyWith(purpose: model));
  }

  void onUpdateJob(MoneyLaunderingItemQuestionModel model) {
    emit(state.copyWith(job: model));
  }

  void onUpdatePosition(MoneyLaunderingItemQuestionModel model) {
    emit(state.copyWith(position: model));
  }
}

class QuestionCodeConst {
  static const String purpose = 'PURPOSE_ACCOUNT';
  static const String job = 'JOB';
  static const String position = 'JOB_POSITION';

  static const String idDefaultPurpose = '6';
  static const String idDefaultJob = '7';
  static const String idDefaultPosition = '5';
}
