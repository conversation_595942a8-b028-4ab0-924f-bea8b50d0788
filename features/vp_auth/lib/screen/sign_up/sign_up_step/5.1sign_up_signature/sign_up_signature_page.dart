import 'dart:io';

import 'package:app/common/utils/app_routes_utils/app_routes_utils.dart';
import 'package:app/common/widgets/app_signature_camera/app_camera_signature_page.dart';
import 'package:app/global/navigator/app_router.dart';
import 'package:app/packages/shared/modules/account/common/assets/account_key_assets.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_utils.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/5.1sign_up_signature/bloc/sign_up_signature_bloc.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/5.sign_up_upload/components/item_description_view.dart';
import 'package:camera/camera.dart';
import 'package:collection/collection.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_ui/widgets/design_system/button/button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../../../../../../common/widgets/constains.dart';

class SignUpSignaturePage extends StatelessWidget {
  const SignUpSignaturePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => SignUpSignatureCubit(),
      child: _SignUpSignaturePage(),
    );
  }
}

class _SignUpSignaturePage extends StatefulWidget {
  const _SignUpSignaturePage({super.key});

  @override
  State<_SignUpSignaturePage> createState() => _SignUpSignaturePageState();
}

class _SignUpSignaturePageState extends State<_SignUpSignaturePage> {
  late SignUpSignatureCubit bloc;

  @override
  void initState() {
    super.initState();
    bloc = context.read<SignUpSignatureCubit>();
  }

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: ColorUtils.bgMain,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          children: [
            BlocBuilder<SignUpSignatureCubit, SignUpSignatureState>(
              buildWhen: (previous, current) =>
                  previous.pathSignature != current.pathSignature,
              builder: (context, state) {
                return Column(
                  children: [
                    ItemDescriptionView(
                      title:
                          getAccountLang(AccountKeyLang.signatureDescription1),
                    ),
                    ItemDescriptionView(
                      title:
                          getAccountLang(AccountKeyLang.signatureDescription2),
                    ),
                  ],
                );
              },
            ),
            BlocBuilder<SignUpSignatureCubit, SignUpSignatureState>(
              buildWhen: (previous, current) =>
                  previous.pathSignature != current.pathSignature,
              builder: (context, state) {
                print('state.pathSignature ${state.pathSignature}');
                return state.pathSignature != ''
                    ? SizedBox(
                        height: MediaQuery.of(context).size.height * 0.3,
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Expanded(
                              child: state.pathSignature != ''
                                  ? Image.file(
                                      File(state.pathSignature),
                                    )
                                  : Image.asset(AccountKeyAssets.uploadGuide),
                            ),
                            // SvgPicture.asset(
                            //   CommonKeyAssets.icCheck2,
                            //   width: 16,
                            //   color: ColorUtils.primary,
                            // ),
                          ],
                        ))
                    : SizedBox();
              },
            ),
            kSpacingHeight16,
            Row(
              children: [
                BlocBuilder<SignUpSignatureCubit, SignUpSignatureState>(
                  buildWhen: (previous, current) =>
                      previous.pathSignature != current.pathSignature,
                  builder: (context, state) {
                    return ButtonWidget(
                      colorBorder: ColorUtils.bgPopup,
                      colorEnable: ColorUtils.primary,
                      actionWidget: Row(
                        children: [
                          SvgPicture.asset(
                            AccountKeyAssets.icCamera,
                            colorFilter: const ColorFilter.mode(
                                ColorDefine.white, BlendMode.srcIn),
                          ),
                          kSpacingWidth4,
                          Text(
                            state.pathSignature != ''
                                ? 'Chụp lại'
                                : 'Chụp ảnh chữ ký',
                            style: TextStyleUtils.text14Weight500
                                .copyWith(color: ColorDefine.white),
                          ),
                        ],
                      ),
                      onPressed: () async {
                        gotoCameraSignature();
                      },
                    );
                  },
                ),
                const SizedBox()
              ],
            )
          ],
        ),
      ),
    );
  }

  Future<void> gotoCameraSignature() async {
    final context = navigation.navigatorKey.currentContext;
    if (context == null) {
      return;
    }
    final isSuccess = await SignUpPermissionUtils.checkPermissionCamera();
    if (isSuccess) {
      final cameras = await availableCameras();
      final camera = cameras.firstWhereOrNull(
          (element) => element.lensDirection == CameraLensDirection.back);

      if (cameras.isNotEmpty && camera != null) {
        RouterUtils.pushFullScreen(
            AppCameraSignaturePage(
              camera: camera,
              title: getAccountLang(AccountKeyLang.yourSignature),
              nameFileSave: 'IDCARD_SIGNATURE',
            ), onCallBack: (pathSave) {
          if (pathSave != null && pathSave is String) {
            print('pathSave');
            print(pathSave);
            bloc.handleCallBackCamera(pathSave);
          }
        });
      }
    }
  }
}
