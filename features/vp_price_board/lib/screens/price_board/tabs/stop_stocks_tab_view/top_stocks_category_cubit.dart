import 'package:flutter/cupertino.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_price_board/core/repository/price_board_repository.dart';
import 'package:vp_price_board/model/enum/top_stocks_type.dart';
import 'package:vp_price_board/model/paging_and_sorting_mixin.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

part 'top_stocks_category_state.dart';

class TopStocksCategoryBloc extends AppCubit<TopStocksCategoryState>
    with StockInfoSocketMixin {
  TopStocksCategoryBloc({required this.repository})
    : super(TopStocksCategoryState(apiStatus: ApiStatus.initial())) {
    loadData();
  }

  final PriceBoardRepository repository;

  Future loadData() async {
    try {
      emit(state.copyWith(apiStatus: ApiStatus.loading()));

      unsubscribeStockInfo();

      final data = await repository.getTop10(
        marketCode: state.marketCode,
        stockType: StockType.ST,
        trend: state.topStocksType.value.$1,
        option: state.topStocksType.value.$2,
        pageSize: 20,
      );

      subscribeStockInfo(data?.content?.map((e) => e.symbol).toSet());

      emit(state.copyWith(apiStatus: ApiStatus.done(), paging: data));
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      emit(state.copyWith(apiStatus: ApiStatus.error(e)));
    }
  }

  void onTypeChange(TopStocksType type) {
    if (type == state.topStocksType) return;

    emit(state.copyWith(topStocksType: type));

    loadData();
  }

  void onExchangeChange(MarketCode marketCode) {
    if (marketCode == state.marketCode) return;

    emit(state.copyWith(marketCode: marketCode));

    loadData();
  }

  Future onRefresh() => loadData();

  void sortBy(SortMode mode) {
    emit(state.copyWith(sortMode: mode));
  }

  @override
  Future<void> close() {
    unsubscribeStockInfo();
    return super.close();
  }
}
