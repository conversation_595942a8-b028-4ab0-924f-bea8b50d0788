part of 'covered_warrant_tab.dart';

class _CoveredWarrantCategoryView extends StatefulWidget {
  const _CoveredWarrantCategoryView({required this.type});

  final CoveredWarrantType type;

  @override
  State<_CoveredWarrantCategoryView> createState() =>
      _CoveredWarrantCategoryViewState();
}

class _CoveredWarrantCategoryViewState
    extends State<_CoveredWarrantCategoryView>
    with AutomaticKeepAliveClientMixin {
  ScrollController controller = ScrollController();

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return MultiBlocProvider(
      providers: [
        BlocProvider<CoveredWarrantCategoryCubit>(
          create:
              (_) => CoveredWarrantCategoryCubit(type: widget.type)..loadData(),
        ),
      ],
      child:
          BlocBuilder<CoveredWarrantCategoryCubit, CoveredWarrantCategoryState>(
            builder: (_, state) {
              return StockListItemBuilderView(
                apiStatus: state.apiStatus,
                data: state.items,
                onRetry: () => onRefresh(context),
                builder: (context, data) {
                  return StockBoardSortView(
                    onChanged: onSort,
                    sortType: state.sortType,
                    status: state.sortStatus,
                    builder: (context, columns) {
                      return StockListView(
                        items: data,
                        columns: columns,
                        controller: controller,
                        onRefresh: () => onRefresh(context),
                      );
                    },
                  );
                },
              );
            },
          ),
    );
  }

  Future onRefresh(BuildContext context) =>
      context.read<CoveredWarrantCategoryCubit>().onRefresh();

  void onSort(BuildContext context, SortMode mode) {
    context.read<CoveredWarrantCategoryCubit>().sortBy(mode);

    controller.jumpTo(0);
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;
}
