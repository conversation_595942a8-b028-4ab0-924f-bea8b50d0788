
import 'package:vp_auth/core/constant/sign_up_type.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/cubit/sign_up_step.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:vp_auth/screen/sign_up/sign_up_step/i_sign_up_step.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';

import 'sign_up_referrer_state.dart';

class RegisterReferenceCubit extends ISignUpController<RegisterReferenceState> {
  RegisterReferenceCubit() : super(RegisterReferenceState());

  bool verifyValid = false;

  String _validAccount = '';

  String _referrerCode = '';

  String get accNum => AppConfigUtils.preAccNum + _referrerCode;

  OnboardingRepository get repository => sl.get<OnboardingRepository>();

  void onTextChanged(String value) {
    _referrerCode = value;

    _onClearTextResponse();

    setEnable(value.isEmpty || verifyValid);

    if (value.length == AppConfigUtils.appOTP) {
      onVerifyReferenceCode(value);
    }
  }

  void onVerifyReferenceCode(String text) async {
    try {
      emit(state.copyWith(loading: true));

      final value = await repository.onboardingUsersBrokers(accNum);

      if (value != null && value.isSuccess()) {
        verifyValid = true;

        setEnable(true);

        _validAccount = accNum;

        emit(state.copyWith(responseText: value.data?.fullName ?? ''));
      } else {
        verifyValid = false;
        setEnable(false);
        emit(state.copyWith(
            responseText:
                getAccountLang(AccountKeyLang.notFoundUserReference)));
      }
    } catch (e) {
      verifyValid = false;
      setEnable(false);
      showError(e);
    } finally {
      emit(state.copyWith(loading: false));
    }
  }

  @override
  void onNext(SignUpTypeScreen type) async {
    if (!verifyValid) {
      if (_referrerCode.isEmpty) {
        try {
          emit(state.copyWith(loading: true));

          await repository.onboardingUsersPutBroker(brokerNeeded: '0');

          SignUpRouter().onPush(SignUpTypeScreen.upload);
        } catch (e) {
          showError(e);
        } finally {
          emit(state.copyWith(loading: false));
        }
      }
      return;
    }

    try {
      emit(state.copyWith(loading: true));

      await Future.wait(
        [
          repository.onboardingUsersPutBroker(brokerNeeded: '0'),
          repository.onUpdateReferenceCode(_validAccount),
        ],
      );

      SignUpRouter().onPush(SignUpTypeScreen.upload);
    } catch (e) {
      showError(e);
    } finally {
      emit(state.copyWith(loading: false));
    }
  }

  void _onClearTextResponse() {
    if (state.responseText.isNullOrEmpty) return;

    verifyValid = false;

    emit(state.copyWith(responseText: ''));
  }

  @override
  bool isTypeScreen(SignUpTypeScreen value) {
    return value == SignUpTypeScreen.referrer;
  }

  @override
  String? get title => getAccountLang(AccountKeyLang.userReference);

  @override
  int? get progress => SignUpStep.referrer.process;

  @override
  bool get enable => _referrerCode.isEmpty || verifyValid;
}
