import 'package:app/common/assets/common_key_assets.dart';
import 'package:app/common/lang/key_lang.dart';
import 'package:app/common/lang/localized_values.dart';
import 'package:app/common/widgets/app_dialog/app_dialog.dart';
import 'package:app/common/widgets/app_dialog/item_dialog_default.dart';
import 'package:app/global/navigator/app_router.dart';
import 'package:app/packages/bond_service/common/lang/bond_key_lang.dart';
import 'package:app/packages/bond_service/common/lang/bond_localized_values.dart';
import 'package:app/packages/shared/modules/account/common/assets/account_key_assets.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/models/onboarding_users_brokers_obj.dart';
import 'package:app/packages/shared/modules/settings/common/lang/settings_key_lang.dart';
import 'package:app/packages/shared/modules/settings/common/lang/settings_localized_values.dart';
import 'package:core_ui/core_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SupporterDialog {
  Future<bool> showDialogInfoBroker(
      OnboardingUsersBrokersResponseObj? obj, String accNo) async {
    bool result = false;
    final context = navigation.navigatorKey.currentContext;
    if (context == null) {
      return false;
    }
    await appShowDialog(
        allowDismiss: true,
        iconWidget: SvgPicture.asset(AccountKeyAssets.icSupporter),
        titleWidget: TitleDialog(
          title: getAccountLang(AccountKeyLang.confirmSupporter),
          titleStyle: TextStyleUtils.text20Weight700
              .copyWith(color: ColorUtils.gray700),
        ),
        contentWidget: Container(
          decoration: BoxDecoration(
              color: ColorUtils.bgPopup,
              borderRadius: BorderRadius.circular(12)),
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              _itemInfoBroker(
                  title: getSettingsLang(SettingsKeyLang.fullName),
                  content: obj?.fullName ?? ''),
              _itemInfoBroker(
                  title: getAccountLang(AccountKeyLang.depositoryAcc),
                  content: accNo),
              (obj?.phone != null)
                  ? _itemInfoBroker(
                      title: getSettingsLang(SettingsKeyLang.phone),
                      content: obj?.phone ?? '----')
                  : const SizedBox.shrink()
            ],
          ),
        ),
        actionWidget: ActionDialog(
            textButtonRight: getBondLang(BondKeyLang.confirm),
            textButtonLeft: getCommonLang(CommonKeyLang.back),
            textStyleLeft: TextStyleUtils.text14Weight600
                .copyWith(color: ColorUtils.gray700),
            colorBorderButtonLeft: ColorUtils.gray700,
            onPressedLeft: () {
              navigation.goBack();
            },
            onPressedRight: () {
              result = true;
              navigation.goBack();
            }));
    return result;
  }

  Widget _itemInfoBroker({String title = '', String content = ''}) {
    return Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Expanded(
          child: Text(title,
              style: vpTextStyle.body14?
                  .copyWith(color: ColorUtils.gray700))),
      Expanded(
          child: Text(content,
              style: vpTextStyle.body14?
                  .copyWith(color: ColorUtils.black)))
    ]);
  }

  void showDialogNotFoundBroker() async {
    final context = navigation.navigatorKey.currentContext;
    if (context == null) {
      return;
    }
    await appShowDialog(
        allowDismiss: true,
        iconWidget:
            SvgPicture.asset(CommonKeyAssets.icWarning, color: ColorUtils.red),
        titleWidget: TitleDialog(
          title: getAccountLang(AccountKeyLang.notFoundSupporterTitle),
          titleStyle: TextStyleUtils.text20Weight700
              .copyWith(color: ColorUtils.gray700),
        ),
        contentWidget: ContentDialog(
            contentStyle: vpTextStyle.body14?
                .copyWith(color: ColorUtils.gray700),
            content: getAccountLang(AccountKeyLang.notFoundSupporterContent)),
        actionWidget: ActionDialog(
            textButtonLeft: getCommonLang(CommonKeyLang.close),
            textStyleLeft: TextStyleUtils.text14Weight600
                .copyWith(color: ColorUtils.gray700),
            colorBorderButtonLeft: ColorUtils.gray700,
            onPressedLeft: () {
              navigation.goBack();
            }));
  }
}
