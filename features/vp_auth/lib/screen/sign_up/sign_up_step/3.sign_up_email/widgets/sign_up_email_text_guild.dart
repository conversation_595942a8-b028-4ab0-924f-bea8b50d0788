import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/3.sign_up_email/sign_up_email_bloc.dart';
import 'package:core_ui/core_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SignUpEmailTextGuild extends StatelessWidget {
  const SignUpEmailTextGuild({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SignUpEmailBloc, SignUpEmailState>(
      buildWhen: (previous, current) =>
          previous.isShowGuide != current.isShowGuide,
      builder: (context, state) {
        return state.isShowGuide
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    getAccountLang(AccountKeyLang.checkEmailGuideTitle),
                    style: vpTextStyle.body14?
                        .copyWith(color: ColorUtils.gray500),
                  ),
                  const _TextGuildItem(
                      content: AccountKeyLang.checkEmailGuideContent1),
                  const _TextGuildItem(
                      content: AccountKeyLang.checkEmailGuideContent2),
                  const _TextGuildItem(
                      content: AccountKeyLang.checkEmailGuideContent3),
                  const _TextGuildItem(
                      content: AccountKeyLang.checkEmailGuideContent4),
                ],
              )
            : const SizedBox.shrink();
      },
    );
  }
}

class _TextGuildItem extends StatelessWidget {
  final String content;

  const _TextGuildItem({Key? key, required this.content}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 26,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 3,
            height: 3,
            margin: const EdgeInsets.only(left: 6, right: 6, top: 2),
            decoration: BoxDecoration(
                shape: BoxShape.circle, color: ColorUtils.gray500),
          ),
          Expanded(
            child: Text(
              getAccountLang(content),
              style: vpTextStyle.body14?
                  .copyWith(color: ColorUtils.gray500),
            ),
          )
        ],
      ),
    );
  }
}
