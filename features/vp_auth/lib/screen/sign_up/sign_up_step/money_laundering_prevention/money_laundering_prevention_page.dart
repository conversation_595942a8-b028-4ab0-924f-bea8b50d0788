import 'package:app/common/widgets/constains.dart';
import 'package:app/common/widgets/vpbank_loading.dart';
import 'package:app/packages/asset/common/assets/asset_key_assets.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/money_laundering_prevention/cubit/money_laundering_cubit.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/money_laundering_prevention/cubit/money_laundering_state.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/money_laundering_prevention/widget/select_value_bottom_sheet.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/money_laundering_prevention/widget/selection_value_widget.dart';
import 'package:app/packages/stock/modules/place_order/presentation/feature/place_order/bloc/place_order_bloc_map_import.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';

class MoneyLaunderingPreventionPage extends StatefulWidget {
  const MoneyLaunderingPreventionPage({Key? key}) : super(key: key);

  @override
  State<MoneyLaunderingPreventionPage> createState() =>
      _MoneyLaunderingPreventionPageState();
}

class _MoneyLaunderingPreventionPageState
    extends State<MoneyLaunderingPreventionPage> {
  late bool check;

  final _cubit = MoneyLaunderingCubit();

  @override
  void initState() {
    super.initState();
    _cubit.onInitData();
    check = _cubit.confirm;
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: ColorUtils.white,
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            kSpacingHeight8,
            BlocBuilder<MoneyLaunderingCubit, MoneyLaunderingState>(
              bloc: _cubit,
              builder: (_, state) {
                if (state.loading) {
                  return SizedBox(
                    width: double.infinity,
                    height: MediaQuery.of(context).size.height * 0.8,
                    child: const Center(child: VPBankLoading()),
                  );
                }
                if (state.emptyData) {
                  return const SizedBox();
                }
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SelectionValueWidget(
                        title: getAccountLang(AccountKeyLang.intendedUse),
                        value: state.purpose,
                        onTap: () => showSelectValueBottomSheet(
                          context: navigation.navigatorKey.currentContext!,
                          list: state.listPurpose ?? [],
                          selected: state.purpose,
                          title: getAccountLang(AccountKeyLang.intendedUse),
                        ).then((value) {
                          if (value != null) {
                            _cubit.onUpdatePurpose(value);
                          }
                        }),
                      ),
                      kSpacingHeight24,
                      SelectionValueWidget(
                        title: getAccountLang(AccountKeyLang.job),
                        value: state.job,
                        onTap: () => showSelectValueBottomSheet(
                          context: navigation.navigatorKey.currentContext!,
                          list: state.listJob ?? [],
                          selected: state.job,
                          title: getAccountLang(AccountKeyLang.job),
                        ).then((value) {
                          if (value != null) {
                            _cubit.onUpdateJob(value);
                          }
                        }),
                      ),
                      kSpacingHeight24,
                      SelectionValueWidget(
                        title: getAccountLang(AccountKeyLang.position),
                        value: state.position,
                        onTap: () => showSelectValueBottomSheet(
                          context: navigation.navigatorKey.currentContext!,
                          list: state.listPosition ?? [],
                          selected: state.position,
                          title: getAccountLang(AccountKeyLang.position),
                        ).then((value) {
                          if (value != null) {
                            setState(() {
                              _cubit.onUpdatePosition(value);
                            });
                          }
                        }),
                      ),
                    ],
                  ),
                );
              },
            ),
            // InkWell(
            //   onTap: () {
            //     setState(() {
            //       check = !check;
            //     });
            //     _cubit.confirm = check;
            //   },
            //   child: Padding(
            //     padding: const EdgeInsets.all(24),
            //     child: Row(
            //       crossAxisAlignment: CrossAxisAlignment.start,
            //       children: [
            //         check
            //             ? SvgPicture.asset(AssetKeyAssets.icCheckBox)
            //             : SvgPicture.asset(AssetKeyAssets.icCheckBoxNone,
            //                 colorFilter: ColorFilter.mode(
            //                   ColorUtils.gray700,
            //                   BlendMode.srcIn,
            //                 )),
            //         kSpacingWidth8,
            //         Expanded(
            //           child: Column(
            //             crossAxisAlignment: CrossAxisAlignment.start,
            //             children: [
            //               Text(
            //                 getAccountLang(AccountKeyLang.customerConfirmation),
            //                 style: vpTextStyle.body14?.copyWith(
            //                   fontWeight: FontWeight.w700,
            //                   color: ColorUtils.gray900,
            //                 ),
            //               ),
            //               ItemTextNote(
            //                 text: getAccountLang(
            //                     AccountKeyLang.customerConfirmation1),
            //               ),
            //               ItemTextNote(
            //                 text: getAccountLang(
            //                     AccountKeyLang.customerConfirmation2),
            //               ),
            //               ItemTextNote(
            //                 text: getAccountLang(
            //                     AccountKeyLang.customerConfirmation3),
            //               ),
            //             ],
            //           ),
            //         ),
            //       ],
            //     ),
            //   ),
            // ),
          ],
        ),
      ),
    );
  }
}

class ItemTextNote extends StatelessWidget {
  const ItemTextNote({Key? key, required this.text}) : super(key: key);
  final String text;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0).copyWith(top: 10),
          child: Icon(
            Icons.circle,
            size: 4,
            color: ColorUtils.gray900,
          ),
        ),
        Expanded(
          child: Text(
            text,
            style: vpTextStyle.body14?.copyWith(
              color: ColorUtils.gray900,
            ),
          ),
        ),
      ],
    );
  }
}

// eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJHZDZhNHlJazQyZU1pTmFTa0ZOb2FhVFpsdjNaNkR1b3RUZkdiclgwaldzIn0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ME4g8cpU8V9WuPLvo81jxlKH9wrYO1x_AunfFyDptuIdwZe2Ng94Tk2QsAmJN_fIYILCtvVi0talFd9HtsSn9206wIEhyf_AzbX72RLDBYZcXZvWe_3nXg2kXgwRMN8H9CfNFTIh43K0dMIVIKVJ7XmujFYPsjx-JhPDwwcN5EJrPtoH0DpuVyYnrvE7umvBb0oSihbDJ1tSV0KhjI4kEO3SMt6jcsSuYvRTId1wer_6zPA0K5_EFP1Yz9jVgR2OvOHp5svy3ymnWOC71m8BQndFJPKn5QPFmWIIT9jMs8JZwyX49qgZEaD0W2DFKuvAP_H-GPyLZOx2_agO34kXIQ
