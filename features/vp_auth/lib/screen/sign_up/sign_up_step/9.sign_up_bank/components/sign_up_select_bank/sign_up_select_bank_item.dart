import 'package:app/common/widgets/divider_widget.dart';
import 'package:app/packages/shared/modules/account/data/model/register/account_bank_branch.dart';
import 'package:app/packages/shared/modules/account/data/model/register/account_bank_list.dart';
import 'package:core_ui/utils/color_utils.dart';
import 'package:core_ui/utils/size_utils.dart';
import 'package:core_ui/utils/text_utils.dart';
import 'package:flutter/material.dart';

class SignUpSelectBankItem<T> extends StatelessWidget {
  const SignUpSelectBankItem({
    Key? key,
    required this.item,
    required this.callback,
  }) : super(key: key);

  final T item;
  final Function(T) callback;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        InkWell(
            child: buildContentView(),
            onTap: () {
              callback(item);
            }),
      ],
    );
  }

  Column buildContentView() {
    String title = '';
    String subTitle = '';
    if (item is AccountBankListResponse) {
      final objBankList = item as AccountBankListResponse;
      title = objBankList.bankcode ?? '';
      subTitle = objBankList.bankname ?? '';
    }
    if (item is AccountBankBranchResponseObj) {
      final objBankBranch = item as AccountBankBranchResponseObj;
      title = objBankBranch.cityname ?? '';
      subTitle = objBankBranch.branchname ?? '';
    }
    return Column(
      children: [
        const SizedBox(height: 16),
        Row(
          children: [
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(title,
                      style: TextStyleUtils.text14Weight600
                          .copyWith(color: ColorUtils.black)),
                  const SizedBox(height: 4),
                  Text(subTitle,
                      style: vpTextStyle.body14?
                          .copyWith(color: ColorUtils.black)),
                ],
              ),
            )
          ],
        ),
        const SizedBox(height: 16),
        DividerWidget()
      ],
    );
  }
}
