import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_centralize/helper/utils.dart';
import 'package:vp_centralize/localization/localize_key.dart';
import 'package:vp_centralize/screen/smart_otp/change_pin_smart_otp/change_pin_success_page.dart';
import 'package:vp_centralize/screen/smart_otp/custom_scaffold.dart';
import 'package:vp_centralize/screen/smart_otp/rx_view.dart';
import 'package:vp_centralize/screen/widgets/input_pin_widget.dart';
import 'package:vp_centralize/screen/widgets/progress_bar.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

import 'forgot_pin_smart_otp_cubit.dart';
import 'forgot_pin_smart_otp_state.dart';

class ForgotPinSmartOTP extends StatefulWidget {
  const ForgotPinSmartOTP({required this.fromRoute, super.key});

  final String fromRoute;

  @override
  State<ForgotPinSmartOTP> createState() => _ForgotPinSmartOTPState();
}

class _ForgotPinSmartOTPState extends State<ForgotPinSmartOTP> {
  late ForgotPinSmartOTPCubit cubit = ForgotPinSmartOTPCubit();

  final pageController = PageController();

  final newPinController = TextEditingController();

  final reNewPinController = TextEditingController();

  int get page =>
      pageController.hasClients ? (pageController.page?.toInt() ?? 0) : 0;

  @override
  void initState() {
    super.initState();

    pageController.addListener(() => cubit.onPageChanged(page));
  }

  @override
  void dispose() {
    cubit.close();
    pageController.dispose();
    newPinController.dispose();
    reNewPinController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ForgotPinSmartOTPCubit>.value(
      value: cubit,
      child: BlocConsumer<ForgotPinSmartOTPCubit, ForgotSmartOTPAbstractState>(
        listener: (_, state) {
          if (state is ForgotSmartOTPErrorState) {
            showError(state.e);
          }

          if (state is GenSmsOTPFailState) {
            showError(state.e);

            /// pop current page (ForgotPinPage)
            Navigator.pop(context);
          }

          if (state is GenSmsOTPSuccessState) {
            onVerifyOtp();
          }
        },
        builder: (_, state) {
          if (state is ForgotSmartOTPSuccessState) {
            return const ChangePinSuccessPage();
          }

          if (cubit.secretKey.isNullOrEmpty) {
            return CustomScaffold(
              bgColor: Colors.transparent,
              loadingStream: cubit.loadingStream,
              child: GestureDetector(
                onTap: () => Navigator.pop(context),
                child: const ColoredBox(
                  color: Colors.transparent,
                  child: SizedBox(height: double.infinity),
                ),
              ),
            );
          }

          return CustomScaffold(
            loadingStream: cubit.loadingStream,
            child: SafeArea(
              child: Column(
                children: [
                  buildHeaderView(),

                  const SizedBox(height: 16),

                  /// content View
                  Expanded(
                    child: PageView(
                      controller: pageController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        InputPinWidget(
                          parentContext: context,
                          controller: newPinController,
                          autoDisposeControllers: false,
                          title: LocalizeKey.forgotSmartInputPinTitle,
                          onChanged: (code) => cubit.onNewPinCodeChanged(code),
                        ),
                        RxView<bool>(
                          rxSubject: cubit.isRenewPinCodeValid,
                          builder: (_, isValid) {
                            return InputPinWidget(
                              parentContext: context,
                              controller: reNewPinController,
                              autoDisposeControllers: false,
                              title: LocalizeKey.forgotSmartInputRePinTitle,
                              error:
                                  !isValid
                                      ? LocalizeKey.forgotSmartInputRePinError
                                      : null,
                              onChanged:
                                  (code) => cubit.onReNewPinCodeChanged(code),
                            );
                          },
                        ),
                      ],
                    ),
                  ),

                  /// build bottom action view
                  buildBottomView(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget buildHeaderView() {
    return HeaderWidget(
      back: false,
      actionRight: () => Navigator.pop(context),
      icon: Icon(CupertinoIcons.clear, color: themeData.gray700),
      subTitle: LocalizeKey.forgotSmartAppBarSubTitle,
      title: LocalizeKey.forgotSmartAppBarTitle,
    );
  }

  Widget buildBottomView() {
    return RxStream(
      dependencies: [cubit.page, cubit.newPinCode, cubit.reNewPinCode],
      builder: (_) {
        return BottomView(
          onBack: () => onBack(),
          onNext: cubit.enableButtonNext ? () => onNext() : null,
        );
      },
    );
  }

  void onBack() {
    if (page == 0) {
      Navigator.pop(context);
    }

    if (page == 1) {
      return pageController.jumpToPage(0);
    }
  }

  void onNext() async {
    if (page == 0 && cubit.newPinCode.value.length == smartPinLength) {
      return pageController.jumpToPage(1);
    }

    if (page == 1 && cubit.newPinCode.value == cubit.reNewPinCode.value) {
      return cubit.verifySmartOTP();
    }
  }

  void onVerifyOtp() {
    CentralizeUtils().verifySmsOTP(
      onSubmitPinCode: (otp, __, ___, ____) {
        return cubit.generationSecretKey(otp!);
      },
      onResendSmsOTP: (__, ___) {
        return cubit.onResendOTP();
      },
      onSuccess: (responseApi) {
        final secretKey = getSecretKey(responseApi);

        if (secretKey != null) {
          cubit.verifySmsOtpSuccess(secretKey);
        }
      },
      onFail: (e) => showError(e),
      onCloseSmsOTPPage: () {
        // context.go(widget.fromRoute);
        final currentLocation = GoRouterState.of(context).uri.toString();
        while (context.canPop() && currentLocation != widget.fromRoute) {
          context.pop();
        }
      },
    );
  }

  String? getSecretKey(Response responseApi) {
    if (responseApi.data is! Map) return null;

    if (responseApi.data['data'] is! Map) return null;

    return responseApi.data['data']['secret_key'];
  }
}
