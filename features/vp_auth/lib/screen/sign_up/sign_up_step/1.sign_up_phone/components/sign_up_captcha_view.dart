import 'dart:async';
import 'dart:io';


import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:recaptcha_enterprise_flutter/recaptcha_action.dart';
import 'package:recaptcha_enterprise_flutter/recaptcha_enterprise.dart';
import 'package:vp_auth/screen/sign_up/sign_up_step/1.sign_up_phone/sign_up_phone_bloc.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class SignUpCaptchaView extends StatefulWidget {
  const SignUpCaptchaView(
      {Key? key,
      required this.onCallBack,
      required this.onCancelLoading,
      this.textEditingController})
      : super(key: key);

  final Function(String result) onCallBack;
  final Function onCancelLoading;
  final TextEditingController? textEditingController;

  @override
  State<SignUpCaptchaView> createState() => _SignUpCaptchaViewState();
}

class _SignUpCaptchaViewState extends State<SignUpCaptchaView> {
  final controllerUI = StreamController<bool>();

  @override
  void initState() {
    super.initState();
    widget.textEditingController?.addListener(() {
      controllerUI.sink.add(true);
    });
  }

  @override
  void dispose() {
    controllerUI.close();
    widget.textEditingController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<SignUpPhoneBloc, SignUpPhoneState>(
      listener: (context, state) {
        if (state.phoneNumber.isNotEmpty) _recaptchaEnterpriseExecute();
      },
      child: SizedBox(
        height: 750,
        child: StreamBuilder<bool>(
          stream: controllerUI.stream,
          builder: (context, snapshot) {
            final value = snapshot.data ?? true;
            return Visibility(
              visible: value,
              child: Container(color: themeData.bgMain),
            );
          },
        ),
      ),
    );
  }

  void _recaptchaEnterpriseExecute({custom = false}) async {
    try {
      var token = custom
          ? await RecaptchaEnterprise.execute(RecaptchaAction.custom('foo'),
              timeout: 10000)
          : await RecaptchaEnterprise.execute(RecaptchaAction.SIGNUP());
      context.read<SignUpPhoneBloc>().verifyRecaptchaEnterprise(token,
          (sessionId) {
        widget.onCallBack(sessionId);
        widget.onCancelLoading();
      });
    } on PlatformException catch (err) {
      context.read<SignUpPhoneBloc>().loading(false);
      showMessage('Code: ${err.code} Message ${err.message}');
    } catch (err) {
      context.read<SignUpPhoneBloc>().loading(false);
      showMessage(err.toString());
    }
  }
}
