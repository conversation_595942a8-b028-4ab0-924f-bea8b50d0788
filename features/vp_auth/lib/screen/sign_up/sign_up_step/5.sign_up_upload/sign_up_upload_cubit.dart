import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:app/common/assets/common_key_assets.dart';
import 'package:app/common/error/show_error.dart';
import 'package:app/common/utils/app_api/base_response.dart';
import 'package:app/common/utils/app_log_utils.dart';
import 'package:app/common/utils/app_snackbar_utils.dart';
import 'package:app/common/widgets/vpbank_loading.dart';
import 'package:app/global/build_config/stock_build_config.dart';
import 'package:app/global/navigator/app_router.dart';
import 'package:app/global/session/app_session.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/data/model/model_ekyc_fis/ocr_ekyc.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/models/onboarding_users_ekyc.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/onboarding_path_api.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/cubit/sign_up_step.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_attach_data.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_tracking.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_utils.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/4.1sign_up_nfc/widget/popup_guide.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/fpt_ocr_ekyc/fpt_helper/fpt_handle_image.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/i_sign_up_step.dart';
import 'package:core/core.dart';
import 'package:core_ui/utils/size_utils.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';

part 'sign_up_upload_state.dart';

/// Fix bug when pass repository to compute
Future<OnboardingResponse<OnboardingUsersEkycResponseObj>?> onboadingUsersEkyc({
  required OnboardingUsersEkycRequestObj param,
  required String onboardingToken,
  required String apiPath,
}) async {
  OnboardingResponse<OnboardingUsersEkycResponseObj>? baseResponse;
  try {
    var client = http.Client();

    final data = await client.post(
      Uri.parse(apiPath),
      body: jsonEncode(param.toJson()),
      headers: {
        KeyAPI.contentType: KeyAPI.appJson,
        KeyAPI.authorization: onboardingToken
      },
    );

    final jsonData = jsonDecode(utf8.decode(data.bodyBytes));

    OnboardingUsersEkycResponseObj? obj;

    if (DataHelper.getMap(jsonData) != null) {
      obj =
          OnboardingUsersEkycResponseObj.fromJson(DataHelper.getMap(jsonData));
    }

    baseResponse = OnboardingResponse<OnboardingUsersEkycResponseObj>.fromJson(
      jsonData,
      data.statusCode,
      data: obj,
    );
  } catch (e) {
    throw HandleError.from(e);
  }

  return baseResponse;
}

Future<OnboardingResponse<OnboardingUsersEkycResponseObj>?> uploadImage(
    Map<String, dynamic> params) async {
  try {
    final imageFront = params['frontId'];
    final imageBack = params['backId'];
    final signature = params['signature'];
    final apiPath = params['apiPath'];
    final onboardingToken = params['onboardingToken'];

    final param = OnboardingUsersEkycRequestObj(
      imageFront: FptHandleImage.convertImageToBase64(imageFront),
      imageBackside: FptHandleImage.convertImageToBase64(imageBack),
      imageSign: FptHandleImage.convertImageToBase64(signature),
    );

    return await onboadingUsersEkyc(
      param: param,
      apiPath: apiPath,
      onboardingToken: onboardingToken,
    );
  } catch (e, stackTrace) {
    debugPrintStack(stackTrace: stackTrace);
    return null;
  }
}

class SignUpUploadCubit extends ISignUpController<SignUpUploadState> {
  SignUpUploadCubit() : super(const SignUpUploadState());

  @override
  String? get title => getAccountLang(AccountKeyLang.captureProfile);

  @override
  int? get progress => SignUpStep.upload.process;

  @override
  void init() {
    final obj = SignUpData().attachDataObj;

    emit(
      state.copyWith(
        pathIdFront: obj.getFrontIdCard(),
        pathIdBack: obj.getBackIdCard(),
      ),
    );

    setEnable(!SignUpData().attachDataObj.isHaveEmpty());
  }

  @override
  bool isTypeScreen(SignUpTypeScreen value) {
    return value == SignUpTypeScreen.upload;
  }

  @override
  void onNext(SignUpTypeScreen type) {
    if (isTypeScreen(type)) {
      goToInfoOcr(SignUpData().dataOCR);
      popupGuide(navigation.navigatorKey.currentContext!,
          onBack: () => SignUpRouter().onPush(SignUpTypeScreen.nfc));
    }
  }

  /* ---------- Xử lý upload Image  ------------ */
  Future apiUploadImage() async {
    showDialogLoading();

    final objIdAttach = SignUpData().attachDataObj;

    try {
      final value = await compute(
        uploadImage,
        {
          'frontId': objIdAttach.getFrontIdCard(),
          'backId': objIdAttach.getBackIdCard(),
          'signature': objIdAttach.getSignature(),
          'onboardingToken': Session().onboardingToken,
          'apiPath': sl.get<StockBuildConfigs>().iamUrl() +
              OnboardingPathAPI.onboardingUsersEkyc,
        },
      );

      if (value != null) {
        _fillToSignUpOCRData(value.data);
        if (value.isSuccess()) {
          SignUpRouter().onPush(SignUpTypeScreen.infoOcr);
          SignUpTracking().signupProfilePhotoSuccess();
        } else {
          showMessage(value.message,
              asset: CommonKeyAssets.icWarning3,
              padding: const EdgeInsets.only(
                bottom: 92,
                left: 32,
                right: 32,
              ));
        }
      }
    } catch (e, stackTrade) {
      debugPrintStack(stackTrace: stackTrade);
      showError(e);
    } finally {
      hideDialogLoading();
    }
  }

  void goToInfoOcr(OCR data) {
    SignUpData().dataOCR = data;
    OnboardingUsersEkycResponseObj obj = OnboardingUsersEkycResponseObj();
    obj.fullName = data.hoVaTen;
    obj.dateOfBirth = data.namSinh;
    obj.nationality = data.quocTich;
    obj.idNumber = data.soCmt;
    obj.address = data.noiTru;
    if (data.diachilienhe == null || data.diachilienhe == '') {
      data.diachilienhe = data.noiTru;
    }
    obj.address2 = data.diachilienhe;
    obj.gender = data.gioiTinh;
    obj.nativePlace = data.quocTich ?? '';
    obj.province = data.chiTietNoiTru?.province ?? '';
    obj.idExpired = data.ngayHetHan;
    obj.idDate = data.ngayCap ?? '';
    obj.idPlace = data.noiCap ?? '';
    //noi tru

    _fillToSignUpOCRData(obj);
  }

  Future<String> saveBase64ToFile(String base64String) async {
    // Chuyển đổi chuỗi Base64 thành mảng byte
    Uint8List bytes = base64Decode(base64String);

    // Lấy thư mục tài liệu của thiết bị
    Directory appDocDir = await getApplicationDocumentsDirectory();
    String appDocPath = appDocDir.path;

    // Tạo đường dẫn đến tệp
    String filePath = '$appDocPath/${generateRandomFileName()}';

    // Tạo tệp và ghi mảng byte vào tệp
    File file = File(filePath);
    await file.writeAsBytes(bytes);
    dlog('File saved at $filePath');
    return filePath;
  }

  String generateRandomFileName() {
    DateTime now = DateTime.now();
    String formattedDate = DateFormat('yyyyMMdd_HHmmss').format(now);
    String randomSuffix = DateTime.now().millisecondsSinceEpoch.toString();
    return 'file_$formattedDate$randomSuffix.png';
  }

  void handleImageCCCD(String fontImage, String backImage) {
    SignUpTracking().signupIdcardFrontSuccess();
    emit(state.copyWith(pathIdFront: fontImage, pathIdBack: backImage));
  }

  void handleCallBackCamera(UploadImageType type, String pathSave,
      {String? base64FrontImage}) {
    if (type == UploadImageType.frontIDCard) {
      SignUpData().attachDataObj.savePathToIdAttachObj(pathSave, type,
          base64ImageFront: base64FrontImage);
      SignUpTracking().signupIdcardFrontSuccess();
      emit(state.copyWith(pathIdFront: pathSave));
    } else if (type == UploadImageType.backIDCard) {
      SignUpData().attachDataObj.savePathToIdAttachObj(pathSave, type);
      SignUpTracking().signupIdcardBackSuccess();
      emit(state.copyWith(pathIdBack: pathSave));
    }
    setEnable(!SignUpData().attachDataObj.isHaveEmpty());
  }

  void _fillToSignUpOCRData(OnboardingUsersEkycResponseObj? data) {
    final obj = SignUpData().ocrData;
    obj.fullname = (data?.fullName ?? '').trim();
    obj.dateofbirth = data?.dateOfBirth ?? '';
    obj.national = data?.nationality ?? '';
    obj.idcode = data?.idNumber ?? '';
    obj.address = data?.address ?? '';
    obj.sex = data?.gender ?? SignUpUtils.male;
    obj.address2 = data?.address2 ?? '';
    obj.country = data?.nativePlace ?? '';
    obj.province = data?.province ?? '';
    obj.expriedDate = data?.idExpired ?? '';
    obj.idDate = data?.idDate ?? '';
    obj.idplace = data?.idPlace ?? '';
  }
}
