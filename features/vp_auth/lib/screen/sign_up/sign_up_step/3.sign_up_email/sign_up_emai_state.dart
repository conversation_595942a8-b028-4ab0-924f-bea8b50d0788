part of 'sign_up_email_bloc.dart';

class SignUpEmailState extends Equatable {
  final bool isLoading;
  final String? msgTextField;
  final String phoneNumber;
  final bool validTextfield;
  final bool isShowGuide;
  final bool isReadOnly;

  const SignUpEmailState(
      {this.isLoading = false,
      this.msgTextField,
      this.phoneNumber = '',
      this.validTextfield = false,
      this.isShowGuide = true,
      this.isReadOnly = false});
  @override
  List<Object?> get props => [
        isLoading,
        msgTextField,
        phoneNumber,
        validTextfield,
        isShowGuide,
        isReadOnly
      ];

  SignUpEmailState copyWith(
      {bool? isLoading,
      String? msgTextField,
      String? phoneNumber,
      bool? validTextfield,
      bool? isShowGuide,
      bool? isReadOnly}) {
    return SignUpEmailState(
        isLoading: isLoading ?? this.isLoading,
        msgTextField: msgTextField,
        phoneNumber: phoneNumber ?? '',
        validTextfield: validTextfield ?? false,
        isShowGuide: isShowGuide ?? this.isShowGuide,
        isReadOnly: isReadOnly ?? this.isReadOnly);
  }
}
