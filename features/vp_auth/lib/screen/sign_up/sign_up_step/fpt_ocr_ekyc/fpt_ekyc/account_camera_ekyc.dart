import 'dart:async';
import 'dart:io';

import 'package:app/common/utils/app_log_utils.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/fpt_ocr_ekyc/fpt_helper/fpt_handle_image.dart';
import 'package:camera/camera.dart';
import 'package:core_ui/utils/color_utils.dart';
import 'package:core_ui/utils/text_utils.dart';
import 'package:flutter/material.dart';

import '../../../../../../data/onboarding/models/onboarding_users_ekyc_action_obj.dart';
import '../fpt_helper/fpt_loading.dart';
import 'account_camera_ekyc_bloc.dart';
import 'account_camera_ekyc_dialog.dart';

class AccountCameraEkyc extends StatefulWidget {
  final CameraDescription camera;
  final OnboardingUsersAccEkycActionResponseObj listAction;

  const AccountCameraEkyc(
      {Key? key, required this.camera, required this.listAction})
      : super(key: key);

  @override
  State<AccountCameraEkyc> createState() => _AccountCameraEkycState();
}

class _AccountCameraEkycState extends State<AccountCameraEkyc> {
  late CameraController controller;
  late Future<void> initiallizeControllerFuture;
  late AccountCameraEkycBloc bloc;

  bool isStopImageStream = false;

  StreamSubscription? subscription;

  @override
  void initState() {
    super.initState();

    controller = Platform.isIOS
        ? CameraController(
            widget.camera,
            ResolutionPreset.high,
            imageFormatGroup: ImageFormatGroup.bgra8888,
            enableAudio: false,
          )
        : CameraController(
            widget.camera,
            ResolutionPreset.high,
            enableAudio: false,
          );

    initiallizeControllerFuture = controller.initialize();

    bloc = AccountCameraEkycBloc();

    subscription = bloc.streamData.listen((event) {
      Navigator.pop(context, event);
    });
  }

  @override
  void dispose() {
    try {
      if (!isStopImageStream && controller.value.isStreamingImages) {
        controller.stopImageStream();
      }

      bloc.timer?.cancel();
      controller.dispose();
      subscription?.cancel();
      bloc.dispose();
    } catch (e) {
      dlog(e);
    }

    if (bloc.pathSavedSelfie.isNotEmpty) {
      for (var path in bloc.pathSavedSelfie) {
        FptHandleImage.deleteFile(path);
      }
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
          backgroundColor: Colors.black87,
          appBar: buildAppBar(context),
          body: buildCameraView()),
    );
  }

  /*-------- Build AppBar -------- */
  AppBar buildAppBar(BuildContext context) {
    return AppBar(
      iconTheme: const IconThemeData(
        color: ColorDefine.white,
      ),
      title: Text(getAccountLang(AccountKeyLang.eKycTitle),
          style: TextStyleUtils.text18Weight700
              .copyWith(color: ColorDefine.white)),
      centerTitle: true,
      backgroundColor: Colors.black87,
    );
  }

  /*-------- Build CameraView -------- */
  FutureBuilder<void> buildCameraView() {
    final size = MediaQuery.of(context).size;
    return FutureBuilder<void>(
      future: initiallizeControllerFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          final cameraPreview = CameraPreview(controller);
          return Stack(children: [
            CustomPaint(
              foregroundPainter: AccountEkycPaint(),
              child: SizedBox(
                  width: controller.value.previewSize?.width,
                  height: controller.value.previewSize?.height,
                  child: cameraPreview),
            ),
            // ),
            SizedBox(
              width: size.width,
              child: ClipPath(clipper: AccountEkycClip(), child: cameraPreview),
            ),
            buildBottomCamera(),
            buildLoading()
          ]);
        } else {
          return const Center(child: CircularProgressIndicator());
        }
      },
    );
  }

  StreamBuilder buildTextGuide() {
    return StreamBuilder<AccountCameraEkycBlocState>(
        stream: bloc.streamStateUI,
        builder: (context, snapshot) {
          final currentState =
              snapshot.data ?? AccountCameraEkycBlocState.hideLoading;
          return getWidgetGuideEkyc(currentState);
        });
  }

  Widget getWidgetGuideEkyc(AccountCameraEkycBlocState state) {
    final stateCounter = state == AccountCameraEkycBlocState.counter;

    switch (state) {
      case AccountCameraEkycBlocState.counter:
      case AccountCameraEkycBlocState.guideEKYC:
        return Align(
          alignment: Alignment.bottomCenter,
          child: Padding(
            padding: const EdgeInsets.only(bottom: 100),
            child: Container(
                height: 100,
                color: Colors.black,
                alignment: Alignment.center,
                child: Text(
                    stateCounter
                        ? '${getAccountLang(AccountKeyLang.ekycStart)} ${bloc.counterStart} s\n${getAccountLang(AccountKeyLang.faceToFrame)}'
                        : bloc.guideEKYC,
                    style: TextStyleUtils.text20Weight700
                        .copyWith(color: ColorDefine.white),
                    textAlign: TextAlign.center)),
          ),
        );
      default:
        return const SizedBox.shrink();
    }
  }

  /*-------- Build Bottom View -------- */
  Align buildBottomCamera() {
    return Align(
        alignment: Alignment.bottomCenter,
        child: IntrinsicHeight(child: buildTextGuide()));
  }

  /*-------- Build ViewLoadingAndDialog -------- */
  StreamBuilder buildLoading() {
    return StreamBuilder<AccountCameraEkycBlocState>(
        stream: bloc.streamStateUI,
        builder: (context, snapshot) {
          final currentState =
              snapshot.data ?? AccountCameraEkycBlocState.showDialog;
          return getWidgetLoadingAndDialog(currentState);
        });
  }

  /*-------- Get Widget By State -------- */
  Widget getWidgetLoadingAndDialog(AccountCameraEkycBlocState state) {
    switch (state) {
      case AccountCameraEkycBlocState.loading:
        return Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          color: Colors.black.withOpacity(0.8),
          child: buidLoadingEkyc(),
        );
      case AccountCameraEkycBlocState.hideLoading:
        return const SizedBox.shrink();
      case AccountCameraEkycBlocState.showDialog:
        return AccountCameraEkycDialog(
          callback: () {
            bloc.sinkState(AccountCameraEkycBlocState.hideLoading);
            captureImage();
          },
        );
      default:
        return const SizedBox.shrink();
    }
  }

  /*-------- Build Loading API -------- */
  Center buidLoadingEkyc() {
    return const Center(
      child: FptLoadingOcrEkyc(),
    );
  }

  /*-------- StartImageStream -------- */
  Future<void> captureImage() async {
    await initiallizeControllerFuture;
    controller.startImageStream((image) => bloc.cameraImage = image);
    bloc.counterAddImage2(widget.listAction, () {
      controller.stopImageStream();
      isStopImageStream = true;
    });
  }
}

class AccountEkycPaint extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    canvas.drawColor(Colors.grey.withOpacity(0.8), BlendMode.dstOut);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return true;
  }
}

class AccountEkycClip extends CustomClipper<Path> {
  @override
  getClip(Size size) {
    final widthCircle = size.width * 0.95;
    final offsetX = (size.width / 2) - (widthCircle / 2);
    final radius = widthCircle / 2;
    Path path = Path()
      ..addRRect(RRect.fromRectAndRadius(
          Rect.fromLTWH(
              offsetX, size.height / 2 - 250, widthCircle, widthCircle),
          Radius.circular(radius)));
    return path;
  }

  @override
  bool shouldReclip(oldClipper) {
    return true;
  }
}
