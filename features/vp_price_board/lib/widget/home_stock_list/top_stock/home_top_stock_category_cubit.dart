import 'package:flutter/cupertino.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_price_board/core/repository/price_board_repository.dart';
import 'package:vp_price_board/model/enum/top_stocks_type.dart';
import 'package:vp_price_board/model/paging_and_sorting_mixin.dart';
import 'package:vp_stock_common/model/enum/chart_period.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

part 'home_top_stock_category_state.dart';

class HomeTopStockCategoryCubit extends AppCubit<HomeTopStockCategoryState>
    with StockInfoSocketMixin {
  HomeTopStockCategoryCubit({required TopStocksType type})
    : super(
        HomeTopStockCategoryState(
          apiStatus: ApiStatus.initial(),
          topStocksType: type,
        ),
      );

  final PriceBoardRepository repository =
      GetIt.instance.get<PriceBoardRepository>();

  final ChartRepository chartRepository = GetIt.instance.get<ChartRepository>();

  Future loadData() async {
    try {
      emit(state.copyWith(apiStatus: ApiStatus.loading()));

      final data = await repository.getTop10(
        stockType: StockType.ST,
        indexCode: IndexCode.VN30,
        trend: state.topStocksType.value.$1,
        option: state.topStocksType.value.$2,
        pageSize: 5,
      );

      final symbols = data?.content?.map((e) => e.symbol).toList();

      subscribeStockInfo(symbols?.toSet());

      emit(
        state.copyWith(
          apiStatus: ApiStatus.done(),
          chartData: await getChartData(symbols),
          paging: data,
        ),
      );
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      emit(state.copyWith(apiStatus: ApiStatus.error(e)));
    }
  }

  Future<Map<String, List<ChartData>>?> getChartData(
    List<String>? symbols,
  ) async {
    try {
      final chartData =
          symbols.isNullOrEmpty
              ? null
              : await chartRepository.getChartData(
                symbols: symbols!,
                period: ChartPeriod.fiveMinutes,
              );

      return chartData;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }

    return null;
  }

  Future onRefresh() => loadData();

  @override
  Future<void> close() {
    unsubscribeStockInfo();
    return super.close();
  }
}
