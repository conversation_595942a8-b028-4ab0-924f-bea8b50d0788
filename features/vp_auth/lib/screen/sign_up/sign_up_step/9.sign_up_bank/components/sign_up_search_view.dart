import 'package:app/common/assets/common_key_assets.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_ui/widgets/design_system/textfield/app_text_field.dart';
import 'package:flutter/material.dart';

class SignUpSearchView extends StatelessWidget {
  const SignUpSearchView({
    Key? key,
    required this.content,
    required this.onChanged,
    required this.textEditingController,
  }) : super(key: key);

  final String content;
  final TextEditingController textEditingController;

  final Function(String) onChanged;

  @override
  Widget build(BuildContext context) {
    return AppTextField(
      onChanged: (value) {
        onChanged(value);
      },
      textController: textEditingController,
      autofocus: true,
      typeInput: TextInputType.text,
      hintText: content,
      prefixPathSVG: CommonKeyAssets.icSearch,
      paddingPrefix: 12,
      paddingBottom: 16,

      // autoFocus: true,
      // inputType: TextInputType.text,
      // hint: content,
      // prefix: Padding(
      //   padding: const EdgeInsets.symmetric(horizontal: 16),
      // child: Icon(Icons.search,
      //     color: ColorUtils.gray900, size: Icon22),
      // ),
    );
  }
}
