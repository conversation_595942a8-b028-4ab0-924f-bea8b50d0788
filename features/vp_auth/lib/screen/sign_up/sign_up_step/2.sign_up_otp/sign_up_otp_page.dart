import 'package:app/common/constants/app_constants.dart';
import 'package:app/common/utils/app_config_utils.dart';
import 'package:app/common/widgets/app_pincode_widget.dart';
import 'package:app/common/widgets/constains.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/2.sign_up_otp/sign_up_otp_bloc.dart';
import 'package:app/packages/shared/modules/money/money_presentation/feature/money_cash_in/view/components/vpbank_loading_money.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:core_ui/core_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SignUpOtpPage extends StatelessWidget {
  const SignUpOtpPage({Key? key, this.sessionID}) : super(key: key);

  final String? sessionID;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => SignUpOtpBloc(sessionID),
      child: _BodySignUpOtpPage(sessionID: sessionID),
    );
  }
}

class _BodySignUpOtpPage extends StatefulWidget {
  const _BodySignUpOtpPage({Key? key, this.sessionID}) : super(key: key);
  final String? sessionID;

  @override
  State<_BodySignUpOtpPage> createState() => _BodySignUpOtpPageState();
}

class _BodySignUpOtpPageState extends State<_BodySignUpOtpPage> {
  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: ColorUtils.bgMain,
      child: const Column(
        children: [Expanded(child: _ContentView()), _Loading()],
      ),
    );
  }
}

class _ContentView extends StatelessWidget {
  final String? sessionId;

  // ignore: unused_element
  const _ContentView({Key? key, this.sessionId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<SignUpOtpBloc>();
    return Padding(
      padding: const EdgeInsets.only(top: 24, left: 24, right: 24),
      child: SingleChildScrollView(
        child: Column(
          children: [
            Text(getAccountLang(AccountKeyLang.systemSendOTP),
                style: TextStyleUtils.text16Weight400,
                textAlign: TextAlign.center),
            kSpacingHeight16,
            _Pincode(),
            BlocBuilder<SignUpOtpBloc, SignUpOtpState>(
              buildWhen: (previous, current) =>
                  previous.messErr != current.messErr,
              builder: (context, state) {
                if (state.messErr.isEmpty) {
                  return const SizedBox();
                }
                return Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: AutoSizeText(state.messErr,
                      maxLines: 2,
                      style: TextStyleUtils.text14Weight500
                          .copyWith(color: ColorUtils.red),
                      textAlign: TextAlign.center),
                );
              },
            ),
            kSpacingHeight8,
            _TimerView(),
            TextButton(
                onPressed: () {
                  bloc.apiGenOTP();
                },
                child: Text(getAccountLang(AccountKeyLang.reSendOTP),
                    style: TextStyleUtils.text14Weight600
                        .copyWith(color: ColorUtils.primary)))
          ],
        ),
      ),
    );
  }
}

class _TimerView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final bloc = context.read<SignUpOtpBloc>();
    return BlocBuilder<SignUpOtpBloc, SignUpOtpState>(
      buildWhen: (previous, current) =>
          previous.isShowTimer != current.isShowTimer,
      builder: (context, state) {
        return state.isShowTimer
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '${getAccountLang(AccountKeyLang.countingOTP)} ',
                    style: vpTextStyle.body14?.copyWith(
                      color: ColorUtils.gray500,
                    ),
                  ),
                  TweenAnimationBuilder(
                    tween: Tween(begin: AppConstants.smsOtpTimeout, end: 0.0),
                    duration:
                        const Duration(seconds: AppConstants.smsOtpTimeout),
                    builder: (_, dynamic value, child) {
                      if (value.toInt() == 0) {
                        bloc.emitMsgErr(
                            getAccountLang(AccountKeyLang.expriedOTP));

                        bloc.emitShowTimer(false);
                      }
                      return Text(
                        '${value.toInt() < 10 ? '0${value.toInt()}' : value.toInt()}',
                        style: TextStyle(color: ColorUtils.primary),
                      );
                    },
                  ),
                  Text(
                    ' s',
                    style: vpTextStyle.body14?.copyWith(
                      color: ColorUtils.gray500,
                    ),
                  )
                ],
              )
            : const SizedBox.shrink();
      },
    );
  }
}

class _Pincode extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final bloc = context.read<SignUpOtpBloc>();
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      height: 40,
      child: AppPincodeWidget(
          controller: bloc.txtPincode,
          count: AppConfigUtils.appOTP,
          onChanged: (value, count) {
            if (value.length < count) {
              bloc.emitMsgErr('');
            }
            bloc.setEnable(value.length == count);
          },
          onCompleted: (value) {
            bloc.apiVerifyOTP();
          }),
    );
  }
}

class _Loading extends StatelessWidget {
  const _Loading({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: BlocBuilder<SignUpOtpBloc, SignUpOtpState>(
        buildWhen: (previous, current) =>
            previous.isLoading != current.isLoading,
        builder: (context, state) {
          return state.isLoading
              ? const Center(
                  child: VPBankLoading(
                  size: 40,
                ))
              : const SizedBox.shrink();
        },
      ),
    );
  }
}
