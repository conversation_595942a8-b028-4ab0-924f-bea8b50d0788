part of 'sign_up_supporter_bloc.dart';

class SignUpSupporterState {
  final SignUpSupporterType type;
  final SignUpSupporterResponseText? responseText;

  const SignUpSupporterState({
    this.type = SignUpSupporterType.notYet,
    this.responseText,
  });

  // @override
  // List<Object?> get props => [type];

  SignUpSupporterState copyWith(
      {SignUpSupporterType? type, SignUpSupporterResponseText? responseText}) {
    return SignUpSupporterState(
      type: type ?? this.type,
      responseText: responseText ?? this.responseText,
    );
  }
}

class SignUpSupporterResponseText {
  final String text;
  final bool? valid;

  SignUpSupporterResponseText({required this.text, this.valid});
}

enum SignUpSupporterType { notYet, had, noNeed }
