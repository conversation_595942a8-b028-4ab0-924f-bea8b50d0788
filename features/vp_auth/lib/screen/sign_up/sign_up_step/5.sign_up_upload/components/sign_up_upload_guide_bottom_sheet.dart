import 'package:app/common/assets/common_key_assets.dart';
import 'package:app/common/utils/app_screen_size_utils.dart';
import 'package:app/common/widgets/constains.dart';
import 'package:app/global/navigator/app_router.dart';
import 'package:app/packages/shared/modules/account/common/assets/account_key_assets.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/5.sign_up_upload/components/sign_up_upload_status_bar.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_ui/widgets/design_system/button/button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class SignUpUploadGuideBottomSheet extends StatefulWidget {
  const SignUpUploadGuideBottomSheet({Key? key, required this.index})
      : super(key: key);
  final int index;

  @override
  State<SignUpUploadGuideBottomSheet> createState() =>
      _SignUpUploadGuideBottomSheetState();
}

class _SignUpUploadGuideBottomSheetState
    extends State<SignUpUploadGuideBottomSheet>
    with SingleTickerProviderStateMixin {
  int _initPage = 0;

  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _initPage = widget.index;
    _pageController = PageController(keepPage: true, initialPage: _initPage);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SignUpUploadStatusBar(
          onChangeTab: (int index) {
            _pageController.jumpToPage(index);
          },
          initPage: _initPage,
        ),
        Expanded(
            child: PageView(
          controller: _pageController,
          onPageChanged: (int page) {
            setState(() {
              _initPage = page;
            });
          },
          children: const [
            _GuideUploadIdCard(
                pathImage: AccountKeyAssets.uploadGuideFocus2,
                guidePath1: AccountKeyAssets.guideFront1,
                guidePath2: AccountKeyAssets.guideFront3,
                guidePath3: AccountKeyAssets.guideFront2),
            _GuideUploadIdCard(
                pathImage: AccountKeyAssets.uploadGuideFocusBack,
                guidePath1: AccountKeyAssets.guideBack1,
                guidePath2: AccountKeyAssets.guideBack2,
                guidePath3: AccountKeyAssets.guideBack3),
          ],
        )),
        Padding(
          padding: const EdgeInsets.all(16),
          child: ButtonWidget(
              action: getAccountLang(AccountKeyLang.understood),
              onPressed: () {
                navigation.goBack();
              }),
        )
      ],
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
}

class _GuideUploadIdCard extends StatelessWidget {
  final String pathImage;
  final String guidePath1;
  final String guidePath2;
  final String guidePath3;
  final _content1 = 'Sử dụng CCCD/CMND bản gốc và còn hiệu lực.';
  final _content2 = 'Đặt giấy tờ nằm thẳng trong khung hình.';
  final _content3 =
      'Đảm bảo tất cả thông tin trên giấy tờ rõ ràng và có thể đọc được. Tránh chụp tối, mờ, lóa sáng.';

  const _GuideUploadIdCard(
      {Key? key,
      required this.pathImage,
      required this.guidePath1,
      required this.guidePath2,
      required this.guidePath3})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Column(children: [
        kSpacingHeight12,
        Image.asset(
          pathImage,
          height: 144,
        ),
        Column(children: [
          _Content(
              content1: _content1, content2: _content2, content3: _content3),
          kSpacingHeight24,
          _ImageContent(path1: guidePath1, path2: guidePath2, path3: guidePath3)
        ]),
      ]),
    );
  }
}

class _Content extends StatelessWidget {
  final String content1;
  final String content2;
  final String content3;

  const _Content(
      {Key? key,
      required this.content1,
      required this.content2,
      required this.content3})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
        child: Column(children: [
      kSpacingHeight12,
      _TextContent(title: content1),
      _TextContent(title: content2),
      _TextContent(title: content3),
    ]));
  }
}

class _TextContent extends StatelessWidget {
  final String title;

  const _TextContent({Key? key, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: SvgPicture.asset(CommonKeyAssets.icCheck2),
          ),
          kSpacingWidth4,
          Expanded(
              child: Text(title,
                  style: vpTextStyle.body14?
                      .copyWith(color: ColorUtils.gray900)))
        ],
      ),
    );
  }
}

class _ImageContent extends StatelessWidget {
  final String path1;
  final String path2;
  final String path3;

  const _ImageContent(
      {Key? key, required this.path1, required this.path2, required this.path3})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        color: ColorUtils.highlightBg,
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        child: Column(children: [
          kSpacingHeight6,
          Text('Hình ảnh không hợp lệ',
              style: TextStyleUtils.text13Weight500
                  .copyWith(color: ColorUtils.gray900)),
          kSpacingHeight12,
          Row(children: [
            Expanded(child: _ImageView(pathImage: path1, title: 'Mờ')),
            kSpacingWidth8,
            Expanded(
                child: _ImageView(
              pathImage: path2,
              title: 'Mất góc',
              isLostAngle: true,
            )),
            kSpacingWidth8,
            Expanded(child: _ImageView(pathImage: path3, title: 'Lóa sáng'))
          ])
        ]),
      ),
    );
  }
}

class _ImageView extends StatelessWidget {
  final String pathImage;
  final String title;
  final bool? isLostAngle;

  const _ImageView(
      {Key? key,
      required this.pathImage,
      required this.title,
      this.isLostAngle = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Container(
            height: (AppScreenSizeUtils.screenWidth - 48) * 0.2,
            decoration: BoxDecoration(
              border: Border.all(color: ColorUtils.red, width: 0.6),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Stack(
              children: [
                Padding(
                  padding: (isLostAngle ?? false)
                      ? const EdgeInsets.only(left: 8, bottom: 8)
                      : const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                  child: Image.asset(
                    pathImage,
                    width: double.infinity,
                    fit: BoxFit.fill,
                  ),
                ),
                Align(
                    alignment: Alignment.bottomRight,
                    child: SvgPicture.asset(
                      CommonKeyAssets.icUnCheck,
                      color: ColorUtils.red,
                      width: 16,
                    ))
              ],
            ),
          ),
        ),
        kSpacingHeight8,
        Text(title,
            style: TextStyleUtils.text13Weight500
                .copyWith(color: ColorUtils.gray900))
      ],
    );
  }
}
