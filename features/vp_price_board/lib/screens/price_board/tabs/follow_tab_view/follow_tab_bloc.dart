import 'package:flutter/cupertino.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_price_board/model/paging_and_sorting_mixin.dart';
import 'package:vp_price_board/model/sorting/paged_sorted_state.dart';
import 'package:vp_stock_common/core/repository/watchlist_repository.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

part 'follow_tab_state.dart';

class FollowTabCubit extends AppCubit<FollowTabState>
    with StockInfoSocketMixin {
  FollowTabCubit() : super(FollowTabState(apiStatus: ApiStatus.initial()));

  WatchlistRepository watchlistRepository =
      GetIt.instance.get<WatchlistRepository>();

  StockCommonRepository stockCommonRepository =
      GetIt.instance.get<StockCommonRepository>();

  Future<BasePagingResponse<StockInfoModel>?> loadDataFor(
    WatchlistModel? watchlist,
  ) async {
    unsubscribeStockInfo();

    final symbols = watchlist?.symbols;

    if (symbols == null || symbols.isEmpty) return null;

    final data = await stockCommonRepository.getStockInfoV2(symbols: symbols);

    subscribeStockInfo(symbols.toSet());

    return data;
  }

  Future loadWatchList() async {
    try {
      emit(state.copyWith(apiStatus: ApiStatus.loading()));

      final watchList = await watchlistRepository
          .getWatchListDefaultForPriceBoard(isLogin: isLoggedIn);

      emit(state.copyWith(watchlist: watchList));

      final paging = await loadDataFor(watchList);

      emit(
        state.copyWith(
          apiStatus: ApiStatus.done(),
          allowPagingNullable: true,
          paging: paging,
        ),
      );
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      emit(state.copyWith(apiStatus: ApiStatus.error(e)));
    }
  }

  Future loadData() async {
    try {
      emit(state.copyWith(apiStatus: ApiStatus.loading()));

      final paging = await loadDataFor(state.watchlist);

      emit(
        state.copyWith(
          apiStatus: ApiStatus.done(),
          allowPagingNullable: true,
          paging: paging,
        ),
      );
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      emit(state.copyWith(apiStatus: ApiStatus.error(e)));
    }
  }

  void onWatchListChanged(List<WatchlistModel>? watchlists) async {
    if (state.watchlist != null && !state.watchlist!.isWatchlistCreated) {
      return;
    }

    final data = watchlists?.findFirstOrNull(
      (e) => e.id == state.watchlist?.id,
    );

    if (data == null) return loadWatchList();

    final oldSymbols = state.watchlist?.symbols ?? const [];
    final newSymbols = data.symbols;

    if (newSymbols.length != oldSymbols.length) {
      return onWatchListSelected(data);
    }

    if (oldSymbols == newSymbols) return;

    onWatchListSelected(data);
  }

  void onWatchListSelected(WatchlistModel watchlist) async {
    emit(state.copyWith(watchlist: watchlist));

    loadData();
  }

  Future onRefresh() {
    if (state.watchlist == null) {
      return loadWatchList();
    }

    return loadData();
  }

  void sortBy(SortMode mode) => emit(state.copyWith(sortMode: mode));

  @override
  Future<void> close() {
    unsubscribeStockInfo();
    return super.close();
  }
}
