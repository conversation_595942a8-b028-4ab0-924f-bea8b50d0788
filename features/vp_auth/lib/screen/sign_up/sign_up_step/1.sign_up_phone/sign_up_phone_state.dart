part of 'sign_up_phone_bloc.dart';

class SignUpPhoneState extends Equatable {
  final bool isLoading;
  final String? msgTextField;
  final String phoneNumber;
  final SignUpPhoneValidTextFieldType validTextFieldType;

  const SignUpPhoneState({
    this.isLoading = false,
    this.msgTextField,
    this.phoneNumber = '',
    this.validTextFieldType = SignUpPhoneValidTextFieldType.defaultType,
  });

  @override
  List<Object?> get props =>
      [isLoading, msgTextField, phoneNumber, validTextFieldType];

  SignUpPhoneState copyWith(
      {bool? isLoading,
      String? msgTextField,
      String? phoneNumber,
      SignUpPhoneValidTextFieldType? validTextfield}) {
    return SignUpPhoneState(
      isLoading: isLoading ?? this.isLoading,
      msgTextField: msgTextField,
      phoneNumber: phoneNumber ?? '',
      validTextFieldType: validTextfield ?? validTextFieldType,
    );
  }
}

enum SignUpPhoneValidTextFieldType {
  defaultType,
  valid,
  invalid,
}
