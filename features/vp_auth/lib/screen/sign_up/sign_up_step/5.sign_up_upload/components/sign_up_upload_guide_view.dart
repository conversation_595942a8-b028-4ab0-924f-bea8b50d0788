import 'dart:io';

import 'package:app/common/assets/common_key_assets.dart';
import 'package:app/common/widgets/app_bottom_sheet.dart';
import 'package:app/common/widgets/constains.dart';
import 'package:app/global/navigator/app_router.dart';
import 'package:app/packages/shared/modules/account/common/assets/account_key_assets.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/5.sign_up_upload/components/sign_up_upload_guide_bottom_sheet.dart';
import 'package:core_ui/core_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class SignUpUploadGuideView extends StatelessWidget {
  const SignUpUploadGuideView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(mainAxisAlignment: MainAxisAlignment.start, children: [
      Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Text('Hướng dẫn chụp ảnh hợp lệ',
                style: vpTextStyle.body14?
                    .copyWith(color: ColorUtils.gray700)),
          ),
          kSpacingWidth8,
          InkWell(
            onTap: () {
              showBottomSheetGuideOcr();
            },
            child: Padding(
              padding: const EdgeInsets.only(top: 10, bottom: 10, left: 12),
              child: Text('Chi tiết',
                  style: TextStyleUtils.text14Weight600
                      .copyWith(color: ColorUtils.primary)),
            ),
          )
        ],
      ),
      kSpacingHeight8,
      Row(crossAxisAlignment: CrossAxisAlignment.start, children: const [
        Expanded(
            child: ItemGuide(
          title: 'Giấy tờ bản gốc và còn hiệu lực',
          pathImage: AccountKeyAssets.uploadGuide,
        )),
        kSpacingWidth8,
        Expanded(
            child: ItemGuide(
          isFocus: true,
          title: 'Đặt giấy tờ nằm trong khung hình',
          pathImage: AccountKeyAssets.uploadGuideFocus,
        )),
        kSpacingWidth8,
        Expanded(
            child: ItemGuide(
                title: 'Đảm bảo thông tin hiển thị rõ ràng',
                pathImage: AccountKeyAssets.uploadGuide)),
      ]),
    ]);
  }
}

class ItemGuide extends StatelessWidget {
  final String title;
  final String pathImage;
  final bool? isFocus;

  const ItemGuide(
      {Key? key,
      required this.title,
      this.pathImage = '',
      this.isFocus = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final _isFocus = isFocus ?? false;
    return Column(
      children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: _isFocus ? 5.5 : 8),
          decoration: BoxDecoration(
              border: Border.all(color: ColorUtils.primary, width: 0.6),
              borderRadius: BorderRadius.circular(8)),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              kSpacingWidth12,
              Expanded(
                  child: pathImage != ''
                      ? Image.file(File(pathImage))
                      : Image.asset(AccountKeyAssets.uploadGuide)),
              SvgPicture.asset(CommonKeyAssets.icCheck2,
                  width: 16, color: ColorUtils.primary)
            ],
          ),
        ),
        kSpacingHeight16,
        Text(title,
            style: TextStyleUtils.text13Weight500
                .copyWith(color: ColorUtils.gray900),
            textAlign: TextAlign.center),
      ],
    );
  }
}

showBottomSheetGuideOcr({int index = 0}) async {
  await showModalBottomSheet(
    context: navigation.navigatorKey.currentContext!,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (BuildContext context) {
      return AppBottomSheet(
          padding: const EdgeInsets.symmetric(vertical: 24),
          isFullSize: true,
          initialChildSize: 0.8 * MediaQuery.of(context).size.height,
          child: SignUpUploadGuideBottomSheet(index: index));
    },
  );
}
