import 'package:app/common/widgets/constains.dart';
import 'package:app/packages/shared/modules/home/<USER>/color_home_utils.dart';
import 'package:app/packages/shared/modules/settings/assets/settings_key_assets.dart';
import 'package:app/packages/stock/modules/place_order/presentation/feature/place_order/bloc/place_order_bloc_map_import.dart';
import 'package:core_ui/widgets/design_system/dialog/notifty_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

Future<void> popupWarning(
  BuildContext context, {
  bool? isInputInfo,
  String? messageError,
  String? title,
  String? textButton,
  VoidCallback? onBack,
}) async {
  await showGeneralDialog(
    context: context,
    barrierDismissible: false,
    barrierLabel: '',
    pageBuilder: (context, _, __) {
      return StatefulBuilder(builder: (context, setState) {
        return BaseDialog(
          title: '',
          contentWidget: Column(mainAxisSize: MainAxisSize.min, children: [
            SvgPicture.asset(
              SettingsKeyAssets.icWarningAccount,
            ),
            kSpacingHeight16,
            Text(
              'Thông báo',
              style: TextStyleUtils.text20Weight700
                  .copyWith(color: ColorUtils.gray900),
            ),
            kSpacingHeight8,
            Text(
              'Quá trình đăng ký bị gián đoạn\nQuý khách vui lòng thử lại.',
              textAlign: TextAlign.center,
              style: vpTextStyle.body14?
                  .copyWith(color: ColorHomeUtils.textSecondary),
            ),
          ]),
          iconSize: 0,
          onPressedLeft: () => navigation.goBack(),
          onPressedRight: () {
            if (onBack != null) {
              navigation.goBack();
              onBack();
              return;
            }
            navigation.goBack();
          },
          textButtonRight: "Đã hiểu",
          colorButtonRight: ColorUtils.primary,
          colorBorderButtonLeft: ColorUtils.gray700,
          textStyleLeft: TextStyleUtils.text14Weight600
              .copyWith(color: ColorUtils.gray700),
          padding:
              const EdgeInsets.only(top: 0, bottom: 16, left: 16, right: 16),
        );
      });
    },
  );
}
