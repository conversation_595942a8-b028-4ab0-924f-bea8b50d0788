part of 'follow_tab_bloc.dart';

class FollowTabState extends PagingAndSortingMixin {
  FollowTabState({required super.apiStatus, super.data, this.watchlist}) {
    sortBySymbols();
  }

  final WatchlistModel? watchlist;

  /// Reorder the stocks list based on the symbol sequence,
  /// because the getStockInfoV2 API does not return the stocks in the same order as the symbols
  void sortBySymbols() {
    if (sortMode != null) return;

    final symbols = watchlist?.symbols;

    if (symbols.isNullOrEmpty || items.isNullOrEmpty) return;

    final symbolOrder = {
      for (var i = 0; i < symbols!.length; i++) symbols[i]: i,
    };

    items?.sort((a, b) {
      final indexA = symbolOrder[a.symbol] ?? 9999;
      final indexB = symbolOrder[b.symbol] ?? 9999;
      return indexA.compareTo(indexB);
    });
  }

  FollowTabState copyWith({
    ApiStatus? apiStatus,
    SortMode? sortMode,
    WatchlistModel? watchlist,
    BasePagingResponse<StockInfoModel>? paging,
    bool allowPagingNullable = false,
  }) {
    var updatedData = data;

    if (paging != null) {
      updatedData = setPaging(paging, updatedData);
    }

    if (sortMode != null) {
      updatedData = sortBy(sortMode, updatedData);
    }

    return FollowTabState(
      apiStatus: apiStatus ?? this.apiStatus,
      watchlist: watchlist ?? this.watchlist,
      data:
          allowPagingNullable && paging == null
              ? PagedSortedState(
                pagingState: PagingState(),
                sortingState: data.sortingState,
              )
              : updatedData,
    );
  }
}
