import 'package:flutter/material.dart';
import 'package:vp_centralize/helper/utils.dart';
import 'package:vp_centralize/localization/localize_key.dart';
import 'package:vp_centralize/model/params/get_sms_otp_params.dart';
import 'package:vp_centralize/router/centralize_router.dart';
import 'package:vp_common/constants/const_otp.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/utils/navigation_service.dart';
import 'package:vp_common/widget/vpbank_loading.dart';
import 'package:vp_core/theme/theme_service.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_settings/router/setting_router.dart';

import 'verify_otp_contact_email_cubit.dart';

class VerifyOtpContactEmail extends StatefulWidget {
  final String? email;

  const VerifyOtpContactEmail({super.key, this.email});

  @override
  State<VerifyOtpContactEmail> createState() => _VerifyOtpContactEmailState();
}

class _VerifyOtpContactEmailState extends State<VerifyOtpContactEmail> {
  late final VerifyOtpContactEmailCubit cubit = VerifyOtpContactEmailCubit();

  String get email => widget.email ?? '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.pop();
      onVerifyOtp();
    });
  }

  /// https://confluence.vpbanks.com.vn/pages/viewpage.action?pageId=********#id-1.X%C3%A1cth%E1%BB%B1cemail-6.2APIg%E1%BB%ADiOTPx%C3%A1cth%E1%BB%B1cEmail
  void onVerifyOtp() async {
    showDialogLoading();
    final tuple2 = await cubit.sendOTP(
      email: email,
      onStart: () => cubit.showLoading(),
      onFinally: () => cubit.hideLoading(),
      onVerifiedInOtherDevice: (e) => cubit.getUserVerify(),
    );
    hideDialogLoading();

    final isSuccess = tuple2.item1;

    final error = tuple2.item2;

    if (!isSuccess) return showError(error);

    CentralizeUtils().verifySmsOTP(
      isCentralize: false,
      otpVerifyBy: OTPVerifyBy.email,
      appbarTitle: LocalizeKey.verifyByEmailTitle,
      appbarSubTitle: LocalizeKey.verifyByEmailSubTitle,
      popWhenSubmitFail: (responseApi) {
        final errorCode = getErrorCode(responseApi);

        return errorCode == emailVerifiedInOtherDevice;
      },
      onSubmitPinCode: (otp, __, ___, ____) {
        return cubit.verifyOTP(otp: otp!, email: email);
      },
      onResendSmsOTP: (__, ___) {
        return cubit.resendOTP(
          email: email,
          onVerifiedInOtherDevice: (e) {
            /// pop sms otp page
            Navigator.pop(context);

            showError(e);

            cubit.getUserVerify();
          },
        );
      },
      onSuccess: (responseApi) {
        cubit.getUserVerify();
        Navigator.of(getContext).popUntil(
          (route) =>
              route.settings.name == SettingRouter.customerInfo.routeName,
        );
        getContext.push(CentralizeRouter.verifyEmailSuccessPage.routeName).then(
          (value) {
            getContext.push(SettingRouter.generalInfo.routeName);
          },
        );
      },
      onFail: (e) {
        showError(e);

        if (e is ResponseError && e.code == emailVerifiedInOtherDevice) {
          cubit.getUserVerify();
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) =>
      VPScaffold(backgroundColor: ColorDefine.transparent);
}
