import 'package:app/packages/shared/modules/account/common/assets/account_key_assets.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/view/components/sign_up_button.dart';
import 'package:core_ui/utils/color_utils.dart';
import 'package:core_ui/utils/size_utils.dart';
import 'package:core_ui/utils/text_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../../../../common/lang/account_key_lang.dart';
import '../../../../../../../common/lang/account_localized_values.dart';

class AccountErrOcrDialog extends StatelessWidget {
  const AccountErrOcrDialog({
    Key? key,
    required this.messageErr,
  }) : super(key: key);
  final String messageErr;
  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Column(children: [
        SvgPicture.asset(AccountKeyAssets.icCaptureDialog),
        const SizedBox(height: 16),
        Text(getAccountLang(AccountKeyLang.unrecognizable),
            style: TextStyleUtils.text20Weight700
                .copyWith(color: ColorUtils.gray700)),
        const SizedBox(height: 24),
        Text(
          messageErr,
          style: vpTextStyle.body14?
              .copyWith(color: ColorUtils.gray700),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),
        SignUpButton(
            text: getAccountLang(AccountKeyLang.retry),
            press: () {
              Navigator.pop(context);
            })
      ]),
    );
  }
}
