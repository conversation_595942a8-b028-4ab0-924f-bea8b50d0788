import 'dart:async';

import 'package:app/common/assets/common_key_assets.dart';
import 'package:app/global/navigator/app_router.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_ui/widgets/design_system/dialog/notifty_dialog.dart';
import 'package:flutter/material.dart';

Future showDialogWarningAccountExist(
  BuildContext context, {
  bool barrierDismissible = true,
}) {
  return showNotifyDialog(
    context: context,
    barrierDismissible: false,
    image: CommonKeyAssets.icWarning,
    colorButtonRight: ColorUtils.primary,
    title: 'Thông báo',
    titleStyle: TextStyleUtils.text20Weight700.copyWith(
      color: ColorUtils.gray700,
    ),
    imagePadding: const EdgeInsets.only(bottom: 12),
    textButtonRight: getAccountLang(AccountKeyLang.understood),
    colorBorderButtonLeft: ColorUtils.gray700,
    content: getAccountLang(AccountKeyLang.existAccountBank),
    onPressedRight: () {
      navigation.goBack();
      Timer(const Duration(milliseconds: 200), () {
        SignUpRouter().onPush(SignUpTypeScreen.accNumber);
      });
    },
  );
}

class _ItemText extends StatelessWidget {
  final Widget? textWidget;
  final String? text;
  final bool havePoint;

  const _ItemText({
    Key? key,
    this.textWidget,
    this.text,
    this.havePoint = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        havePoint
            ? Container(
                width: 3,
                height: 3,
                margin:
                    const EdgeInsets.symmetric(horizontal: 6).copyWith(top: 10),
                decoration: BoxDecoration(
                  color: ColorUtils.gray700,
                  shape: BoxShape.circle,
                ),
              )
            : const SizedBox(),
        Expanded(
            child: textWidget ??
                Text(
                  text ?? '',
                  style: vpTextStyle.body14?.copyWith(
                    color: ColorUtils.gray700,
                  ),
                ))
      ],
    );
  }
}
