import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tuple/tuple.dart';
import 'package:vp_centralize/generated/assets.gen.dart';
import 'package:vp_centralize/localization/localize_key.dart';
import 'package:vp_centralize/model/params/get_smart_otp_params.dart';
import 'package:vp_centralize/router/centralize_router.dart';
import 'package:vp_centralize/screen/centralize_orientation/centralize_orientation_page.dart';
import 'package:vp_centralize/screen/smart_otp/custom_scaffold.dart';
import 'package:vp_centralize/screen/smart_otp/gen_smart_otp/gen_smart_otp_page.dart';
import 'package:vp_centralize/screen/smart_otp/rx_view.dart';
import 'package:vp_centralize/screen/widgets/dialog/notifty_dialog.dart';
import 'package:vp_centralize/screen/widgets/input_pin_widget.dart';
import 'package:vp_centralize/screen/widgets/keep_session_view.dart';
import 'package:vp_centralize/screen/widgets/progress_bar.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

import 'get_smart_otp_cubit.dart';
import 'get_smart_otp_state.dart';

void showRegisterSmartOTPDialog({
  required BuildContext context,
  bool registerInOtherDevice = false,
  String? deviceRegistered,
}) {
  VPPopup.outlineAndPrimaryButton(
        title: LocalizeKey.unregisterSmartOTPTitleType2,
        content: LocalizeKey.unregisterSmartOTPContentType2,
      )
      .copyWith(
        icon: SvgPicture.asset(
          Assets.icons.icWarning.path,
          package: 'vp_common',
        ),
        button: VpsButton.secondarySmall(
          title: LocalizeKey.unregisterSmartOTPButtonCloseType2,
          onPressed: () => context.pop(),
        ),
      )
      .copyWith(
        button: VpsButton.primarySmall(
          title: LocalizeKey.unregisterSmartOTPButtonRegisterType2,
          onPressed: () {
            /// pop dialog
            context.pop(context);

            context.push(
              CentralizeRouter.registerSmartOTPSplash.routeName,
              extra: Tuple2(registerInOtherDevice, deviceRegistered),
            );
          },
        ),
      )
      .showDialog(context);
}

showDialogBiometricNotEnroll(BuildContext context) {
  showNotifyDialog(
    allowDismiss: true,
    context: context,
    iconSize: 80,
    image: CentralizeKeyAssets.icons.icCancel.path,
    imagePadding: const EdgeInsets.only(bottom: 24),
    onPressedLeft: () => Navigator.pop(context),
    title: LocalizeKey.biometricNotEnrolledTitle,
    content: LocalizeKey.biometricNotEnrolledContent,
    textButtonLeft: LocalizeKey.biometricNotEnrolledBtClose,
    colorButtonLeft: themeData.white,
    colorBorderButtonLeft: themeData.gray500,
    textStyleLeft: vpTextStyle.subtitle14.copyColor(themeData.gray700),
  );
}

showDialogBiometricLocked(BuildContext context) {
  showNotifyDialog(
    allowDismiss: true,
    context: context,
    iconSize: 80,
    imagePadding: const EdgeInsets.only(bottom: 24),
    onPressedLeft: () => Navigator.pop(context),
    title: LocalizeKey.biometricLockedTitle,
    content: LocalizeKey.biometricLockedReason,
    colorButtonRight: themeData.red,
    textButtonRight: LocalizeKey.close,
    onPressedRight: () => Navigator.pop(context),
    image: CentralizeKeyAssets.icons.close.path,
  );
}

class GetSmartOtpPage extends StatefulWidget {
  const GetSmartOtpPage({required this.params, super.key});

  final GetSmartOTPParams? params;

  @override
  State<GetSmartOtpPage> createState() => _GetSmartOtpState();
}

class _GetSmartOtpState extends State<GetSmartOtpPage> {
  String? get accountNo => widget.params?.accountNo;

  bool get allowKeepSession => widget.params?.allowKeepSession ?? false;

  bool get showButtonForgotPin => widget.params?.showButtonForgotPin ?? false;

  late GetSmartOTPCubit cubit = GetSmartOTPCubit(accountNo);

  final pinController = TextEditingController();

  final focusNode = FocusNode();

  @override
  void dispose() {
    cubit.close();
    focusNode.dispose();
    pinController.dispose();
    super.dispose();
  }

  void addOnBackEvent() {
    // AppTracking.instance.logEvent(
    //   name: AppTrackingEvent.loginUnlockSmartOtpBack,
    //   type: TrackingType.appsflyer,
    // );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        addOnBackEvent();

        return true;
      },
      child: CentralizeOrientationPage(
        child: BlocProvider<GetSmartOTPCubit>.value(
          value: cubit,
          child: BlocConsumer<GetSmartOTPCubit, GetSmartOTPAbstractState>(
            listener: (_, state) {
              if (state is BiometricNotEnrolledState) {
                showDialogBiometricNotEnroll(context);
              }
            },
            builder: (_, state) {
              if (state is SmartOTPPinValidState) {
                return GenSmartOTPPage(
                  pinCode: state.pin,
                  accountNo: accountNo,
                );
              }

              return CustomScaffold(
                child: SafeArea(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      buildHeaderView(),

                      /// content view
                      Expanded(child: buildContentView()),

                      KeepSessionView(
                        allowKeepSession: allowKeepSession,
                        onChanged:
                            (value) => widget.params?.onKeepSessionChanged
                                ?.call(value),
                      ),

                      /// bottom view
                      RxView(
                        rxSubject: cubit.pinCode,
                        builder: (_, __) {
                          return BottomView(
                            onBack: () => Navigator.pop(context),
                            onNext:
                                cubit.enableButtonNext
                                    ? () => cubit.checkPinCodeIsValid(
                                      cubit.pinCode.value,
                                    )
                                    : null,
                          );
                        },
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget buildContentView() {
    return SingleChildScrollView(
      child: Column(
        children: [
          const SizedBox(height: 16),

          /// build input pin view
          RxView<String>(
            rxSubject: cubit.errorMessage,
            builder: (_, errorMessage) {
              return InputPinWidget(
                parentContext: context,
                controller: pinController,
                autoDisposeControllers: false,
                onChanged: (code) => cubit.onPinChanged(code),
                title: LocalizeKey.getSmartOTPInputTitle,
                error: errorMessage,
              );
            },
          ),

          /// button forgot smart otp
          if (showButtonForgotPin)
            TextButton(
              onPressed: () => onForgotPin(),
              child: Text(
                LocalizeKey.changeSmartForgotButton,
                style: vpTextStyle.subtitle14.copyColor(themeData.primary),
              ),
            ),

          const SizedBox(height: 16),

          RxStream(
            dependencies: [
              cubit.biometricAvailable,
              cubit.biometricLocked,
              cubit.hasFingerPrint,
            ],
            builder: (_) {
              final available = cubit.biometricAvailable.value;

              if (!available) return const SizedBox(height: 0);

              final locked = cubit.biometricLocked.value;

              return GestureDetector(
                onTap: locked ? null : () => onAuthenticateBiometric(),
                child: SvgPicture.asset(
                  biometricIcon,
                  width: 40,
                  height: 40,
                  package: CentralizeKeyAssets.package,
                  color: locked ? themeData.gray500 : themeData.primary,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  String get biometricIcon {
    if (Platform.isIOS) return CentralizeKeyAssets.icons.faceid.path;

    return cubit.hasFingerPrint.value
        ? CentralizeKeyAssets.icons.fingerprint.path
        : CentralizeKeyAssets.icons.faceid.path;
  }

  Widget buildHeaderView() {
    return HeaderWidget(
      actionRight: () {
        addOnBackEvent();

        Navigator.pop(context);
      },
      back: false,
      subTitle: LocalizeKey.getSmartOTPTitle,
      title: LocalizeKey.getSmartOTPSubTitle,
      icon: Icon(CupertinoIcons.clear, color: themeData.gray700),
    );
  }

  void onAuthenticateBiometric() async {
    clearInputPin();
    focusNode.unfocus();
    cubit.onAuthenticateBiometric();
  }

  void onForgotPin() async {
    final isForgotSuccess = await context.push(
      CentralizeRouter.forgotPinSmartOTP.routeName,
      extra: CentralizeRouter.smartOTP.routeName,
    );

    if (isForgotSuccess == true) {
      cubit.checkBiometricAvailable();
    }
  }

  void clearInputPin() => pinController.clear();
}
