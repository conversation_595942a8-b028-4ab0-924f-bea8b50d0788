import 'package:app/common/utils/app_keyboard_utils.dart';
import 'package:app/common/utils/app_text_input_formatter_utils.dart';
import 'package:app/common/widgets/constains.dart';
import 'package:app/global/firebase/remote_config/remote_config_service.dart';
import 'package:app/packages/asset/common/assets/asset_key_assets.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/data/model/banner/banner_entity.dart';
import 'package:app/packages/shared/modules/account/data/model/register/account_bank_branch.dart';
import 'package:app/packages/shared/modules/account/data/model/register/account_bank_list.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/9.sign_up_bank/components/sign_up_bank_note_view.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/9.sign_up_bank/components/sign_up_select_bank/sign_up_select_bank.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/9.sign_up_bank/sign_up_bank_cubit.dart';
import 'package:app/shared/image_view.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_ui/utils/theme_utils.dart';
import 'package:core_ui/widgets/design_system/drop_widget/app_spinner_widget.dart';
import 'package:core_ui/widgets/design_system/textfield/app_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';

class SignUpBankPage extends StatelessWidget {
  const SignUpBankPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
        create: ((_) => SignUpBankCubit()), child: _BodySignUpBankPage());
  }
}

class _BodySignUpBankPage extends StatefulWidget {
  @override
  State<_BodySignUpBankPage> createState() => _BodySignUpBankPageState();
}

class _BodySignUpBankPageState extends State<_BodySignUpBankPage> {
  late SignUpBankCubit bloc;

  @override
  void initState() {
    super.initState();

    bloc = context.read<SignUpBankCubit>();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<SignUpBankCubit, SignUpBankState>(
      bloc: bloc,
      listener: (context, state) {},
      child: ColoredBox(
        color: ColorUtils.bgMain,
        child: SingleChildScrollView(
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 24, left: 16, right: 16),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      buildSpinnerBank(),
                      kSpacingHeight8,
                      buildSpinnerBankBranch(),
                      kSpacingHeight16,
                      buildTxtBankAccount(),
                      kSpacingHeight8,
                      buildTxtAccountName(),
                      kSpacingHeight8,
                      const SignUpBankNoteView(),
                      kSpacingHeight16,
                      if (SignUpData().bankSuccess) ...[
                        BlocProvider.value(
                          value: bloc,
                          child: _AcceptRegister(),
                        ),
                        kSpacingHeight16,
                        StreamBuilder<BannerEntity?>(
                            stream: RemoteConfigService()
                                .getBannerBankOnboarding()
                                .asStream(),
                            builder: (context, snapshot) {
                              if (snapshot.hasData &&
                                  ((snapshot.data?.imageLink ?? '')
                                          .isNotEmpty &&
                                      (snapshot.data?.show ?? false))) {
                                return ImageView(
                                  snapshot.data?.imageLink,
                                  fit: BoxFit.cover,
                                  borderRadius: BorderRadius.circular(8),
                                );
                              }

                              return const SizedBox.shrink();
                            }),
                      ]
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /* -------- Spinner Bank --------*/
  StreamBuilder buildSpinnerBank() {
    return StreamBuilder(
      stream: bloc.streamBank,
      builder: (context, snapshot) {
        String content = '';
        if (snapshot.hasData && snapshot.data is String) {
          content = snapshot.data as String;
        }
        return AppSpinnerWidget(
          title: getAccountLang(AccountKeyLang.yourBankInfo),
          content: content.isNotEmpty
              ? content
              : getAccountLang(AccountKeyLang.selectBank),
          colorBorder: ThemeUtils().isDarkMode()
              ? ColorDefine.transparent
              : ColorUtils.borderDisable,
          isHaveSelect: content.isNotEmpty,
          tap: () => gotoListBank(),
        );
      },
    );
  }

  /* -------- Toi bottom tim kiem bank --------*/
  void gotoListBank() async {
    AppKeyboardUtils.dismissKeyboard();

    if (bloc.listBank.isEmpty) {
      await bloc.getBankList(context);

      if (bloc.listBank.isNotEmpty) {
        showSelectBank();
      }
    } else {
      showSelectBank();
    }
  }

  /* -------- Spinner Chi nhanh Bank --------*/
  StreamBuilder buildSpinnerBankBranch() {
    return StreamBuilder<AccountBankBranchResponseObj?>(
      stream: bloc.streamBankBranch,
      builder: (context, snapshot) {
        final data = snapshot.data;

        final content = data?.branchname ?? '';

        return AppSpinnerWidget(
          content: content.isNotEmpty
              ? content
              : getAccountLang(AccountKeyLang.branch),
          isHaveSelect: content.isNotEmpty,
          colorBorder: ThemeUtils().isDarkMode()
              ? ColorDefine.transparent
              : ColorUtils.borderDisable,
          tap: () => onSelectBankBranch(),
        );
      },
    );
  }

  void onSelectBankBranch() async {
    if (bloc.bankSelected == null) return;

    if (bloc.listBankBranch.isEmpty) {
      await bloc.callApiGetBankBranch(bloc.bankSelected!.bankid!);
      showSelectBankBranch();
    } else {
      showSelectBankBranch();
    }
  }

  /* -------- Textfield Bank Account --------*/
  Widget buildTxtBankAccount() {
    return BlocBuilder<SignUpBankCubit, SignUpBankState>(
      buildWhen: (previous, current) => current is SignUpBankStateAccNoBank,
      builder: (context, state) {
        String? message;
        if (state is SignUpBankStateAccNoBank) {
          message =
              bloc.txtAccountNumber.text.isEmpty ? null : state.msgErrAccNoBank;
        }

        return AppTextField(
            autofocus: false,
            listFormatter: [
              FilteringTextInputFormatter.allow(RegExp('[0-9a-zA-Z]'))
            ],
            textController: bloc.txtAccountNumber,
            onChanged: (value) {},
            maxLength: 20,
            messsage: message,
            isSuccess: true,
            hintText: getAccountLang(AccountKeyLang.accountNumber),
            errorStyle: vpTextStyle.body14?.copyWith(
              color: ColorUtils.red,
            ),
            typeInput: TextInputType.text);
      },
    );
  }

  /* -------- Textfield Bank Account --------*/
  Widget buildTxtAccountName() {
    return BlocBuilder<SignUpBankCubit, SignUpBankState>(
      buildWhen: (previous, current) => current is SignUpBankStateAccNameBank,
      builder: (context, state) {
        String? message;
        if (state is SignUpBankStateAccNameBank) {
          message =
              bloc.txtAccountName.text.isEmpty ? null : state.msgErrAccNameBank;
        }

        return AppTextField(
            autofocus: false,
            isUpperCaseFirst: true,
            listFormatter: [
              FilteringTextInputFormatter.allow(RegExp('[a-zA-Z -]')),
              UpperCaseTextFormatter()
            ],
            messsage: message,
            isSuccess: message == null,
            textController: bloc.txtAccountName,
            onChanged: (value) {},
            maxLength: 70,
            errorStyle: vpTextStyle.body14?.copyWith(
              color: ColorUtils.red,
            ),
            hintText: getAccountLang(AccountKeyLang.bankAccName),
            typeInput: TextInputType.text);
      },
    );
  }

  /* -------- Show List Bank --------*/
  Future<void> showSelectBank() async {
    final bankSelect = await showModalBottomSheet<AccountBankListResponse>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Padding(
          padding: MediaQuery.of(context).viewInsets,
          child: SignUpSelectBank<AccountBankListResponse>(list: bloc.listBank),
        );
      },
    );

    if (bankSelect != null) {
      await Future.delayed(const Duration(milliseconds: 250));

      await bloc.callApiGetBankBranch(bankSelect.bankid ?? 0);

      bloc.setBankNameSelect(bankSelect);
    }
  }

  /* -------- Show List Chi Nhanh Bank --------*/
  Future<void> showSelectBankBranch() async {
    final bankBranchSelect =
        await showModalBottomSheet<AccountBankBranchResponseObj>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Padding(
          padding: MediaQuery.of(context).viewInsets,
          child: SignUpSelectBank<AccountBankBranchResponseObj>(
            list: bloc.listBankBranch,
          ),
        );
      },
    );

    if (bankBranchSelect != null) {
      bloc.setBankBranchSelect(bankBranchSelect);
    }
  }
}

class _AcceptRegister extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _AcceptRegisterState();
  }
}

class _AcceptRegisterState extends State<_AcceptRegister> {
  late SignUpBankCubit bloc;
  bool _check = SignUpData().bankSuccess;

  @override
  void initState() {
    super.initState();

    bloc = context.read<SignUpBankCubit>();
    bloc.accept = _check;
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        setState(() {
          _check = !_check;
          bloc.accept = _check;
          SignUpData().setComboWithBank(bloc.accept);
        });
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _check
              ? SvgPicture.asset(AssetKeyAssets.icCheckBox)
              : SvgPicture.asset(AssetKeyAssets.icCheckBoxNone,
                  colorFilter: ColorFilter.mode(
                    ColorUtils.gray700,
                    BlendMode.srcIn,
                  )),
          kSpacingWidth8,
          Expanded(
            child: Text(
              'Tôi muốn đăng ký tài khoản ngân hàng VPBank ',
              style: vpTextStyle.body14?.copyWith(
                color: ColorUtils.gray900,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
