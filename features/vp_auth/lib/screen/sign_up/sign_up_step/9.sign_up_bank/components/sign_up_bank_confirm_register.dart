import 'package:app/common/assets/common_key_assets.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/9.sign_up_bank/components/sign_up_bank_banner.dart';
import 'package:app/packages/stock/modules/place_order/presentation/feature/place_order/bloc/place_order_bloc_map_import.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SignUpBankConfirmRegister extends StatefulWidget {
  final bool value;

  final Function(bool status) onChangeState;

  final VoidCallback onBannerClick;

  const SignUpBankConfirmRegister({
    Key? key,
    required this.onChangeState,
    required this.onBannerClick,
    this.value = true,
  }) : super(key: key);

  @override
  State<SignUpBankConfirmRegister> createState() => _SignUpBankConfirmState();
}

class _SignUpBankConfirmState extends State<SignUpBankConfirmRegister> {
  void _onTap() {
    widget.onChangeState(!widget.value);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          onTap: () => _onTap(),
          child: Row(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0).copyWith(right: 8),
                child: SvgPicture.asset(widget.value
                    ? CommonKeyAssets.icCheckBoxRect
                    : CommonKeyAssets.icCheckBoxNoneRect),
              ),
              Expanded(
                child: Text(
                  'Tôi muốn đăng ký tài khoản ngân hàng VPBank',
                  style: vpTextStyle.body14?
                      .copyWith(color: ColorUtils.gray900),
                ),
              ),
              const SizedBox(width: 16),
            ],
          ),
        ),
        SignUpBankBanner(onTap: widget.onBannerClick),
      ],
    );
  }
}
