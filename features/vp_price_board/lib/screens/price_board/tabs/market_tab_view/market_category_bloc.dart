import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_price_board/model/paging_and_sorting_mixin.dart';

import 'package:vp_stock_common/vp_stock_common.dart';

part 'market_category_state.dart';

class MarketCategoryBloc extends AppCubit<MarketCategoryState>
    with StockInfoSocketMixin {
  MarketCategoryBloc({
    StockCommonRepository? repository,
    required this.marketCode,
  }) : repository = repository ?? GetIt.instance.get<StockCommonRepository>(),
       super(MarketCategoryState(apiStatus: ApiStatus.initial()));

  final StockCommonRepository repository;

  final MarketCode marketCode;

  Future<List<String>?> loadCache() async {
    try {
      final data = await repository.getMarketListCache(
        marketCode: marketCode,
        stockType: StockType.ST,
      );

      return data;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }

    return null;
  }

  Future loadData() async {
    try {
      final cache = await loadCache();


      if (cache != null) {
        emit(
          state.copyWith(
            apiStatus: ApiStatus.done(),
            stockCached: cache.sublist(0, min(20, cache.length)),
          ),
        );
      } else {
        emit(state.copyWith(apiStatus: ApiStatus.loading()));
      }

      final data = await repository.getMarketList(
        marketCode: marketCode,
        stockType: StockType.ST,
      );

      subscribeStockInfo(data?.content?.map((e) => e.symbol).toSet());

      emit(state.copyWith(apiStatus: ApiStatus.done(), paging: data));
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      emit(state.copyWith(apiStatus: ApiStatus.error(e)));
    }
  }

  Future onRefresh() => loadData();

  void sortBy(SortMode mode) {
    emit(state.copyWith(sortMode: mode));
  }

  @override
  Future<void> close() {
    unsubscribeStockInfo();
    return super.close();
  }
}
