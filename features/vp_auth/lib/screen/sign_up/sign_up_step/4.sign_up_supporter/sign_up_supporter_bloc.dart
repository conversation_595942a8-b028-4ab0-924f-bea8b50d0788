import 'dart:async';

import 'package:app/common/error/show_error.dart';
import 'package:app/common/utils/app_config_utils.dart';
import 'package:app/common/utils/app_snackbar_utils.dart';
import 'package:app/common/widgets/vpbank_loading.dart';
import 'package:app/global/analytics/utils/deeplink_data.dart';
import 'package:app/packages/shared/modules/account/common/utils/account_utils.dart';
import 'package:app/packages/shared/modules/account/data/onboarding/onboarding_repository.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/cubit/sign_up_step.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_router.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_page/helper/sign_up_tracking.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/i_sign_up_step.dart';
import 'package:app/packages/shared/modules/settings/common/lang/settings_key_lang.dart';
import 'package:app/packages/shared/modules/settings/common/lang/settings_localized_values.dart';
import 'package:core/di/injector.dart';
import 'package:flutter/material.dart';

part 'sign_up_supporter_state.dart';

class SignUpSupporterBloc extends ISignUpController<SignUpSupporterState> {
  SignUpSupporterBloc() : super(const SignUpSupporterState());

  TextEditingController txtSupporter = TextEditingController();

  String _brokerNo = '';

  String _dataTextField = '';

  @override
  String? get title => getSettingsLang(SettingsKeyLang.caringStaff);

  @override
  int? get progress => SignUpStep.support.process;

  @override
  void init() {
    _checkFromDeepLink();
  }

  @override
  bool isTypeScreen(SignUpTypeScreen value) {
    return value == SignUpTypeScreen.supporter;
  }

  @override
  void onNext(SignUpTypeScreen type) {
    if (isTypeScreen(type)) {
      apiUpdateBroker();
    }
  }

  void emitType(SignUpSupporterType type) {
    if (!isDisableSelect()) {
      emit(state.copyWith(type: type));
      _checkEnable();
    }
  }

  bool isNoSupporter() {
    return [SignUpSupporterType.noNeed, SignUpSupporterType.notYet]
        .contains(state.type);
  }

  void _checkEnable() {
    setEnable(isNoSupporter() ||
        (state.type == SignUpSupporterType.had &&
            txtSupporter.text.length >= AppConfigUtils.appOTP));
  }

  void _checkFromDeepLink() {
    if (SignUpData().isPre116C() && !DeepLinkData().isRoleRM()) {
      final value = AccountUtils.replace116C(SignUpData().refcustodycd);
      if (value.isNotEmpty) {
        emit(state.copyWith(type: SignUpSupporterType.had));
        txtSupporter.value = TextEditingValue(text: value);
        Timer(const Duration(milliseconds: 500), () {
          apiGetInfoBroker();
        });
        setEnable(true);
      }
    } else {
      emit(state.copyWith(type: SignUpSupporterType.notYet));
      setEnable(true);
    }
  }

  bool isDisableSelect() {
    return SignUpData().isPre116C() &&
        DeepLinkData().isRoleMG() &&
        state.type == SignUpSupporterType.had;
  }

  void onChangeTextFieldSupporter(String value) async {
    if (value.length < AppConfigUtils.appOTP) {
      emit(state.copyWith(
          responseText: SignUpSupporterResponseText(
        text: '',
      )));
    }
    if (_dataTextField == value) {
      return;
    } else {
      _dataTextField = value;
    }
    if (value.length < AppConfigUtils.appOTP) {
      setEnable(value.length >= AppConfigUtils.appOTP);
      return;
    }
    apiGetInfoBroker();
  }

  void apiGetInfoBroker() async {
    try {
      const preAcc = AppConfigUtils.preAccNum;
      final accNo = '$preAcc${txtSupporter.text}';
      showLoading();
      final value =
          await sl.get<OnboardingRepository>().onboardingUsersBrokers(accNo);
      if (value?.data != null) {
        _brokerNo = accNo;
        emit(state.copyWith(
            responseText: SignUpSupporterResponseText(
          text: value?.data?.fullName ?? '',
          valid: true,
        )));
        setEnable(true);
      } else {
        setEnable(false);
        emit(state.copyWith(
            responseText: SignUpSupporterResponseText(
          text: value?.message ?? '',
          valid: false,
        )));
      }
    } catch (e) {
      setEnable(false);
      _brokerNo = '';
      emit(state.copyWith(
          responseText: SignUpSupporterResponseText(
        text: '',
        valid: false,
      )));
      showError(e);
    } finally {
      hideLoading();
    }
  }

  void apiUpdateBroker() async {
    final map = {
      SignUpSupporterType.noNeed.name: '0',
      SignUpSupporterType.notYet.name: '1',
      SignUpSupporterType.had.name: '2'
    };
    SignUpTracking()
        .signupBrokerSelectOption(state.type == SignUpSupporterType.noNeed
            ? 'no_want'
            : state.type == SignUpSupporterType.had
                ? 'had'
                : 'want');
    try {
      showDialogLoading();
      final value = await sl
          .get<OnboardingRepository>()
          .onboardingUsersPutBroker(
              brokerNeeded: map[state.type.name] ?? '0', brokerNo: _brokerNo);
      if (value != null) {
        if (value.isSuccess()) {
          SignUpRouter().onPush(SignUpTypeScreen.upload);
          SignUpTracking().signupBrokerRegSuccess();
        } else {
          showMessage(value.message);
          SignUpTracking().signupBrokerRegFail(value.message);
        }
      }
    } catch (e) {
      showError(e);
      SignUpTracking().signupBrokerRegFail(e);
    } finally {
      hideDialogLoading();
    }
  }
}
