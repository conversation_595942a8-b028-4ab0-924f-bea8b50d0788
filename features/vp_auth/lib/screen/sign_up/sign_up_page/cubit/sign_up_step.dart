class SignUpStep {
  final int process;
  final int percent;

  SignUpStep({required this.process, required this.percent});

  static final phone = SignUpStep(process: 10, percent: 0);
  static final email = SignUpStep(percent: 20, process: 20);
  static final support = SignUpStep(process: 30, percent: 30);
  static final referrer = SignUpStep(process: 30, percent: 30);
  static final upload = SignUpStep(process: 40, percent: 40);
  static final nfc = SignUpStep(process: 45, percent: 45);
  static final info = SignUpStep(process: 50, percent: 50);
  static final ekyc = SignUpStep(process: 60, percent: 60);
  static final bank = SignUpStep(process: 70, percent: 70);
  static final moneyLaundering = SignUpStep(process: 75, percent: 75);
  static final accNum = SignUpStep(process: 80, percent: 80);
  static final contract = SignUpStep(process: 90, percent: 90);
}
