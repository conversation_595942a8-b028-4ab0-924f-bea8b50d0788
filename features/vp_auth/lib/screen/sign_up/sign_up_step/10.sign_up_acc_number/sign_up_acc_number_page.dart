import 'package:app/common/utils/app_config_utils.dart';
import 'package:app/common/widgets/app_pincode_widget.dart';
import 'package:app/common/widgets/vpbank_loading.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_key_lang.dart';
import 'package:app/packages/shared/modules/account/common/lang/account_localized_values.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/10.sign_up_acc_number/sign_up_acc_number_cubit.dart';
import 'package:app/packages/shared/modules/account/presentation/features/sign_up/sign_up_step/10.sign_up_acc_number/sign_up_acc_number_state.dart';
import 'package:core_ui/utils/color_utils.dart';
import 'package:core_ui/utils/size_utils.dart';
import 'package:core_ui/utils/text_utils.dart';
import 'package:core_ui/widgets/design_system/button/button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SignUpAccNumberPage extends StatelessWidget {
  const SignUpAccNumberPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
        create: ((_) => SignUpAccNumberCubit()),
        child: _BodySignUpAccNumberPage());
  }
}

class _BodySignUpAccNumberPage extends StatefulWidget {
  @override
  State<_BodySignUpAccNumberPage> createState() =>
      _BodySignUpAccNumberPageState();
}

class _BodySignUpAccNumberPageState extends State<_BodySignUpAccNumberPage> {
  late SignUpAccNumberCubit bloc;

  @override
  void initState() {
    super.initState();

    bloc = context.read<SignUpAccNumberCubit>();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: ColorUtils.bgMain,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(getAccountLang(AccountKeyLang.selectAccNumber),
                  style: TextStyleUtils.text16Weight600
                      .copyWith(color: ColorUtils.black)),
              _buildContent(),
              IntrinsicWidth(
                  child: ButtonWidget(
                onPressed: () {
                  bloc.callApiSuggestCustId(true);
                },
                padding: const EdgeInsets.all(14),
                textStyle: TextStyleUtils.text14Weight600
                    .copyWith(color: ColorUtils.black),
                colorEnable: ColorUtils.transparent,
                action: getAccountLang(AccountKeyLang.autoFindAcc),
                colorBorder: ColorUtils.black,
              )),
              const SizedBox(height: 16),
              _Loading()
            ],
          ),
        ),
      ),
    );
  }

  Row _buildContent() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const SizedBox(width: SizeUtils.kRadius16),
        Text(AppConfigUtils.preAccNum,
            style: TextStyleUtils.text16Weight600
                .copyWith(color: ColorUtils.gray700)),
        const SizedBox(width: 16),
        Expanded(
            child: Padding(
                padding: const EdgeInsets.only(top: 20),
                child: AppPincodeWidget(
                    controller: bloc.txtControllerAccNum,
                    count: AppConfigUtils.appOTP,
                    onChanged: (value, count) {
                      if (!bloc.valid(value)) {
                        bloc.setEnable(false);
                      }
                    },
                    onCompleted: (value) {
                      bloc.callAPICheckExitsAccNum();
                    }))),
      ],
    );
  }
}

class _Loading extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SignUpAccNumberCubit, SignUpAccNumberState>(
      buildWhen: (previous, current) => previous.isLoading != current.isLoading,
      builder: (context, state) {
        return state.isLoading
            ? const Center(child: VPBankLoading())
            : const SizedBox.shrink();
      },
    );
  }
}
