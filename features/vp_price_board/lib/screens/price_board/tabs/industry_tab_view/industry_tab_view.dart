import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_price_board/core/repository/price_board_repository.dart';
import 'package:vp_price_board/model/industry_model.dart';
import 'package:vp_price_board/widget/sort/normal_sort_view.dart';
import 'package:vp_price_board/widget/stock_list_item.dart';
import 'package:vp_price_board/widget/stock_list_item_builder_view.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

import 'industry_tab_cubit.dart';

part 'widgets/industry_bottom_sheet.dart';

class IndustryTabView extends StatefulWidget {
  const IndustryTabView({super.key});

  @override
  State<IndustryTabView> createState() => _IndustryTabViewState();
}

class _IndustryTabViewState extends State<IndustryTabView>
    with AutomaticKeepAliveClientMixin {
  ScrollController controller = ScrollController();

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create:
              (_) => IndustryTabCubit(
                repository: GetIt.instance.get<PriceBoardRepository>(),
              )..loadData(),
        ),
      ],
      child: BlocBuilder<IndustryTabCubit, IndustryTabState>(
        builder: (context, state) {
          return Column(
            children: [
              if (state.industries.hasData)
                Container(
                  padding: const EdgeInsets.fromLTRB(12, 10, 12, 10),
                  width: double.infinity,
                  child: VPDropdownView.small(
                    value: state.industrySelected?.name,
                    onTap:
                        () async => selectIndustry(context, state.industries),
                  ),
                ),
              Expanded(
                child: StockListItemBuilderView(
                  apiStatus: state.apiStatus,
                  data: state.sortedData,
                  onRetry: () => onRefresh(context),
                  builder: (context, data) {
                    return NormalSortView(
                      onChanged: onSort,
                      sortType: state.sortMode?.sortType,
                      status: state.sortMode?.status,
                      builder: (context, columns) {
                        return StockListView(
                          items: data,
                          columns: columns,
                          controller: controller,
                          onRefresh: () => onRefresh(context),
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Future onRefresh(BuildContext context) =>
      context.read<IndustryTabCubit>().onRefresh();

  void selectIndustry(
    BuildContext context,
    List<IndustryModel>? industries,
  ) async {
    final result = await _showIndustryBottomSheet(
      context,
      industries: industries,
      industrySelected: context.read<IndustryTabCubit>().state.industrySelected,
    );

    if (result is! IndustryModel || !context.mounted) return;

    context.read<IndustryTabCubit>().onIndustryChanged(result);
  }

  void onSort(BuildContext context, SortMode mode) {
    context.read<IndustryTabCubit>().sortBy(mode);

    controller.jumpTo(0);
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;
}
