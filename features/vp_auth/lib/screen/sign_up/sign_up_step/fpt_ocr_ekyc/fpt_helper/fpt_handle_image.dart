import 'dart:convert';
import 'dart:io';

import 'package:app/common/utils/app_log_utils.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';

class FptHandleImage {
  static const jpg = '.jpg';
  static const png = '.png';

  // Xoa file
  static Future<void> deleteFile(String path) async {
    File file = File(path);
    try {
      if (await file.exists()) {
        await file.delete();
        dlog('Delete file: $path success');
      }
    } catch (e) {
      dlog('Delete File: ${e.toString()}');
    }
  }

  // Save image from imagePicker
  static saveImageFromImagePicker(String pathXFile, String nameFileSave,
      Function(bool, String) callBack) async {
    final bytes = await File(pathXFile).readAsBytes();
    final img.Image? capturedImage = img.decodeImage(bytes);
    if (capturedImage != null) {
      final img.Image orientedImage =
          img.bakeOrientation(capturedImage); /* <PERSON>ử lý xoay lại ảnh */

      Directory appDocDir = await getApplicationDocumentsDirectory();
      String appDocPath = appDocDir.path;
      String pathSave =
          '$appDocPath/${nameFileSave}_${DateTime.now().microsecondsSinceEpoch}${FptHandleImage.jpg}';

      await FptHandleImage.deleteFile(
          pathXFile); /*  Xoá file đã lưu vào từ camera */

      await File(pathSave)
          .writeAsBytes(img.encodeJpg(orientedImage, quality: 90));
      dlog('File OCR Save: $pathSave'); /* Lưu ảnh vào trong bộ nhớ */
      callBack(true, pathSave);
    }
    callBack(false, '');
  }

  // Convert to Base64String
  static String convertImageToBase64(String path) {
    if (path.isEmpty) {
      return '';
    }
    final bytes = File(path).readAsBytesSync();
    return base64Encode(bytes);
  }
}
