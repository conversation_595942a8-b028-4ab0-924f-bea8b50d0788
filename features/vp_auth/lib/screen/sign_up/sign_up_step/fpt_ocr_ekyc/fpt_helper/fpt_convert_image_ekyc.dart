import 'dart:ffi';
import 'dart:io';

import 'package:app/common/utils/app_log_utils.dart';
import 'package:camera/camera.dart';
import 'package:flutter/cupertino.dart';
import 'package:image/image.dart' as img;
import 'package:image/image.dart';
import 'package:path_provider/path_provider.dart';

import 'fpt_handle_image.dart';

// ignore: camel_case_types
typedef convert_func = Pointer<Uint32> Function(
    Pointer<Uint8>, Pointer<Uint8>, Pointer<Uint8>, Int32, Int32, Int32, Int32);
typedef Convert = Pointer<Uint32> Function(
    Pointer<Uint8>, Pointer<Uint8>, Pointer<Uint8>, int, int, int, int);

class FptConvertImageEkyc {
  final DynamicLibrary convertImageLib = Platform.isAndroid
      ? DynamicLibrary.open("libconvertImage.so")
      : DynamicLibrary.process();
  late Convert conv;

  FptConvertImageEkyc() {
    try {
      conv = convertImageLib
          .lookup<NativeFunction<convert_func>>('convertImage')
          .asFunction<Convert>();
    } catch (e) {
      dlog(e);
    }
  }

  img.Image _convertYUV420(CameraImage image) {
    final uvRowStride = image.planes[1].bytesPerRow;
    final uvPixelStride = image.planes[1].bytesPerPixel ?? 0;
    final newImage = img.Image(width: image.width, height: image.height);
    for (final p in newImage) {
      final x = p.x;
      final y = p.y;
      final uvIndex =
          uvPixelStride * (x / 2).floor() + uvRowStride * (y / 2).floor();
      final index = y * uvRowStride +
          x; // Use the row stride instead of the image width as some devices pad the image data, and in those cases the image width != bytesPerRow. Using width will give you a distored image.
      final yp = image.planes[0].bytes[index];
      final up = image.planes[1].bytes[uvIndex.toInt()];
      final vp = image.planes[2].bytes[uvIndex.toInt()];
      p.r = (yp + vp * 1436 / 1024 - 179).round().clamp(0, 255).toInt();
      p.g = (yp - up * 46549 / 131072 + 44 - vp * 93604 / 131072 + 91)
          .round()
          .clamp(0, 255)
          .toInt();
      p.b = (yp + up * 1814 / 1024 - 227).round().clamp(0, 255).toInt();
    }

    return newImage;
  }

  /* Phần xử lý này dùng C. Về độ an toàn sẽ có thể chạy hầu hết trên các device */
  Future<String?> convertImageStreamWithC(CameraImage _savedImage) async {
    try {
      if (Platform.isAndroid) {
        final imgResult = _convertYUV420(_savedImage);

        final isRotate = imgResult.width > imgResult.height;

        if (!isRotate) return getPathSave(imgResult);

        final img.Image orientedImage = img.copyRotate(imgResult, angle: -90);

        return getPathSave(orientedImage);
      } else if (Platform.isIOS) {
        return await convertImageStreamByIOS(_savedImage);
      }
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);

      return null;
    }

    return null;
  }

  /// Xu ly tren thiet bi IOS
  Future<String> convertImageStreamByIOS(CameraImage image) async {
    var imgResult = img.Image.fromBytes(
      width: image.width,
      height: image.height,
      bytes: image.planes[0].bytes.buffer,
      order: ChannelOrder.bgra,
    );
    final isRotate = imgResult.width > imgResult.height;

    if (isRotate) {
      final img.Image orientedImage = img.copyRotate(imgResult, angle: -90);

      return getPathSave(orientedImage);
    } else {
      return getPathSave(imgResult);
    }
  }

  /// Xu ly luu anh ekyc
  Future<String> getPathSave(img.Image imgResult) async {
    Directory appDocDir = await getApplicationDocumentsDirectory();
    dlog('Path: ${appDocDir.path}');
    String appDocPath = appDocDir.path;
    String pathSave =
        '$appDocPath/idcard_sefie_${DateTime.now().microsecondsSinceEpoch}${FptHandleImage.jpg}';

    await File(pathSave).writeAsBytes(img.encodeJpg(imgResult, quality: 75));

    return pathSave;
  }
}
