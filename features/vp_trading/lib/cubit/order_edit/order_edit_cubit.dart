import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:vp_common/error/show_error.dart';
import 'package:vp_common/utils/app_helper.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/repository/command_history_repository.dart';
import 'package:vp_trading/model/request/order/update_order_request.dart';

part 'order_edit_state.dart';

class OrderEditCubit extends Cubit<OrderEditState> {
  OrderEditCubit() : super(const OrderEditState());

  final CommandHistoryRepository _commandHistoryRepository =
      GetIt.instance<CommandHistoryRepository>();

  /// Reset error message
  void resetErrorMessage() {
    emit(state.copyWith(errorMessage: null));
  }

  /// Reset success message
  void resetSuccessMessage() {
    emit(state.copyWith(successMessage: null));
  }

  /// Modify order with new price and volume
  Future<void> modifyOrder({
    required String orderId,
    required String accountId,
    required String symbol,
    required double newPrice,
    required int newVolume,
    String market = 'equity',
  }) async {
    try {
      if (isClosed) return;

      emit(
        state.copyWith(
          status: OrderEditStatus.loading,
          errorMessage: null,
          successMessage: null,
        ),
      );

      final updateRequest = UpdateOrderRequest(
        accountId: accountId,
        orderId: orderId,
        market: market,
        via: 'V',
        requestId: AppHelper().genXRequestID(),
        qty: newVolume,
        price: newPrice.toInt().toString(),
      );

      final response = await _commandHistoryRepository.editOrder(updateRequest);

      if (isClosed) return;

      if (response.isSuccess) {
        emit(
          state.copyWith(
            status: OrderEditStatus.success,
            successMessage: 'Order modified successfully',
          ),
        );
      }
    } catch (error) {
      if (isClosed) return;

      var message = (await getErrorMessage(error));
      emit(
        state.copyWith(status: OrderEditStatus.failure, errorMessage: message),
      );
      debugPrintStack(stackTrace: StackTrace.current);
    } finally {
      emit(state.copyWith(status: OrderEditStatus.initial));
    }
  }
}
